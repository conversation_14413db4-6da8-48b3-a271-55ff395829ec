<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">93%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-28 10:33 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app\__init__.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b___init___py.html">app\api\__init__.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bb44a478d70e6240___init___py.html">app\api\api_v1\__init__.py</a></td>
                <td class="name left"><a href="z_bb44a478d70e6240___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bb44a478d70e6240_api_py.html">app\api\api_v1\api.py</a></td>
                <td class="name left"><a href="z_bb44a478d70e6240_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437___init___py.html">app\api\api_v1\endpoints\__init__.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_fields_py.html">app\api\api_v1\endpoints\fields.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_fields_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_generation_py.html">app\api\api_v1\endpoints\generation.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_generation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>56</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="56 56">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_tables_py.html">app\api\api_v1\endpoints\tables.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_tables_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_workflows_py.html">app\api\api_v1\endpoints\workflows.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_workflows_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html">app\core\__init__.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t10">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t10"><data value='Settings'>Settings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t28">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html#t28"><data value='Config'>Settings.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_database_py.html">app\core\database.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_database_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="8 12">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html">app\models\__init__.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_generation_py.html#t11">app\models\generation.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_generation_py.html#t11"><data value='GenerationStatus'>GenerationStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_generation_py.html#t19">app\models\generation.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_generation_py.html#t19"><data value='GenerationTask'>GenerationTask</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_generation_py.html">app\models\generation.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_generation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_relationship_py.html#t12">app\models\relationship.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_relationship_py.html#t12"><data value='RelationshipType'>RelationshipType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_relationship_py.html#t19">app\models\relationship.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_relationship_py.html#t19"><data value='TableRelationship'>TableRelationship</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_relationship_py.html">app\models\relationship.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_relationship_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_table_field_py.html#t12">app\models\table_field.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_table_field_py.html#t12"><data value='FieldType'>FieldType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_table_field_py.html#t27">app\models\table_field.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_table_field_py.html#t27"><data value='TableField'>TableField</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_table_field_py.html">app\models\table_field.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_table_field_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_workflow_py.html#t11">app\models\workflow.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_workflow_py.html#t11"><data value='WorkflowStatus'>WorkflowStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_workflow_py.html#t19">app\models\workflow.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_workflow_py.html#t19"><data value='Workflow'>Workflow</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_workflow_py.html#t36">app\models\workflow.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_workflow_py.html#t36"><data value='DataTable'>DataTable</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_workflow_py.html">app\models\workflow.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_workflow_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174___init___py.html">app\schemas\__init__.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_field_py.html#t10">app\schemas\field.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_field_py.html#t10"><data value='TableFieldBase'>TableFieldBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_field_py.html#t23">app\schemas\field.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_field_py.html#t23"><data value='TableFieldCreate'>TableFieldCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_field_py.html#t28">app\schemas\field.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_field_py.html#t28"><data value='TableFieldUpdate'>TableFieldUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_field_py.html#t41">app\schemas\field.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_field_py.html#t41"><data value='TableFieldInDB'>TableFieldInDB</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_field_py.html#t48">app\schemas\field.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_field_py.html#t48"><data value='Config'>TableFieldInDB.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_field_py.html#t52">app\schemas\field.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_field_py.html#t52"><data value='TableField'>TableField</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_field_py.html">app\schemas\field.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_field_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t10">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t10"><data value='GenerationRequest'>GenerationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t15">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t15"><data value='GenerationTaskInDB'>GenerationTaskInDB</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t29">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t29"><data value='Config'>GenerationTaskInDB.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t33">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t33"><data value='GenerationTask'>GenerationTask</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t38">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html#t38"><data value='GenerationResult'>GenerationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_relationship_py.html#t10">app\schemas\relationship.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_relationship_py.html#t10"><data value='TableRelationshipBase'>TableRelationshipBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_relationship_py.html#t18">app\schemas\relationship.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_relationship_py.html#t18"><data value='TableRelationshipCreate'>TableRelationshipCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_relationship_py.html#t23">app\schemas\relationship.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_relationship_py.html#t23"><data value='TableRelationshipUpdate'>TableRelationshipUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_relationship_py.html#t29">app\schemas\relationship.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_relationship_py.html#t29"><data value='TableRelationshipInDB'>TableRelationshipInDB</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_relationship_py.html#t36">app\schemas\relationship.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_relationship_py.html#t36"><data value='Config'>TableRelationshipInDB.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_relationship_py.html#t40">app\schemas\relationship.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_relationship_py.html#t40"><data value='TableRelationship'>TableRelationship</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_relationship_py.html">app\schemas\relationship.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_relationship_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_table_py.html#t9">app\schemas\table.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_table_py.html#t9"><data value='DataTableBase'>DataTableBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_table_py.html#t17">app\schemas\table.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_table_py.html#t17"><data value='DataTableCreate'>DataTableCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_table_py.html#t22">app\schemas\table.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_table_py.html#t22"><data value='DataTableUpdate'>DataTableUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_table_py.html#t30">app\schemas\table.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_table_py.html#t30"><data value='DataTableInDB'>DataTableInDB</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_table_py.html#t37">app\schemas\table.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_table_py.html#t37"><data value='Config'>DataTableInDB.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_table_py.html#t41">app\schemas\table.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_table_py.html#t41"><data value='DataTable'>DataTable</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_table_py.html">app\schemas\table.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_table_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html#t10">app\schemas\workflow.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html#t10"><data value='WorkflowBase'>WorkflowBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html#t16">app\schemas\workflow.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html#t16"><data value='WorkflowCreate'>WorkflowCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html#t21">app\schemas\workflow.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html#t21"><data value='WorkflowUpdate'>WorkflowUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html#t28">app\schemas\workflow.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html#t28"><data value='WorkflowInDB'>WorkflowInDB</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html#t35">app\schemas\workflow.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html#t35"><data value='Config'>WorkflowInDB.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html#t39">app\schemas\workflow.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html#t39"><data value='Workflow'>Workflow</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html#t44">app\schemas\workflow.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html#t44"><data value='WorkflowList'>WorkflowList</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html">app\schemas\workflow.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html">app\services\__init__.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_deepseek_service_py.html#t14">app\services\deepseek_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_deepseek_service_py.html#t14"><data value='DeepSeekService'>DeepSeekService</data></a></td>
                <td>56</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="40 56">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_deepseek_service_py.html">app\services\deepseek_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_deepseek_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_field_service_py.html#t10">app\services\field_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_field_service_py.html#t10"><data value='FieldService'>FieldService</data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_field_service_py.html">app\services\field_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_field_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_table_service_py.html#t10">app\services\table_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_table_service_py.html#t10"><data value='TableService'>TableService</data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_table_service_py.html">app\services\table_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_table_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html#t10">app\services\workflow_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html#t10"><data value='WorkflowService'>WorkflowService</data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html">app\services\workflow_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>619</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="576 619">93%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-28 10:33 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
