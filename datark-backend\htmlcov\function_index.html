<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">93%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-28 10:33 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app\__init__.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4a9cca768ff3c21b___init___py.html">app\api\__init__.py</a></td>
                <td class="name left"><a href="z_4a9cca768ff3c21b___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bb44a478d70e6240___init___py.html">app\api\api_v1\__init__.py</a></td>
                <td class="name left"><a href="z_bb44a478d70e6240___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bb44a478d70e6240_api_py.html#t16">app\api\api_v1\api.py</a></td>
                <td class="name left"><a href="z_bb44a478d70e6240_api_py.html#t16"><data value='health_check'>health_check</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bb44a478d70e6240_api_py.html">app\api\api_v1\api.py</a></td>
                <td class="name left"><a href="z_bb44a478d70e6240_api_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437___init___py.html">app\api\api_v1\endpoints\__init__.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_fields_py.html#t15">app\api\api_v1\endpoints\fields.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_fields_py.html#t15"><data value='create_field'>create_field</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_fields_py.html#t24">app\api\api_v1\endpoints\fields.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_fields_py.html#t24"><data value='get_fields_by_table'>get_fields_by_table</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_fields_py.html#t33">app\api\api_v1\endpoints\fields.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_fields_py.html#t33"><data value='get_field'>get_field</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_fields_py.html#t48">app\api\api_v1\endpoints\fields.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_fields_py.html#t48"><data value='update_field'>update_field</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_fields_py.html#t64">app\api\api_v1\endpoints\fields.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_fields_py.html#t64"><data value='delete_field'>delete_field</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_fields_py.html">app\api\api_v1\endpoints\fields.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_fields_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_generation_py.html#t18">app\api\api_v1\endpoints\generation.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_generation_py.html#t18"><data value='run_generation_task'>run_generation_task</data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_generation_py.html#t60">app\api\api_v1\endpoints\generation.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_generation_py.html#t60"><data value='generate_data'>generate_data</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_generation_py.html#t99">app\api\api_v1\endpoints\generation.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_generation_py.html#t99"><data value='get_generation_task'>get_generation_task</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_generation_py.html">app\api\api_v1\endpoints\generation.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_generation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_tables_py.html#t15">app\api\api_v1\endpoints\tables.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_tables_py.html#t15"><data value='create_table'>create_table</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_tables_py.html#t24">app\api\api_v1\endpoints\tables.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_tables_py.html#t24"><data value='get_tables_by_workflow'>get_tables_by_workflow</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_tables_py.html#t33">app\api\api_v1\endpoints\tables.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_tables_py.html#t33"><data value='get_table'>get_table</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_tables_py.html#t48">app\api\api_v1\endpoints\tables.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_tables_py.html#t48"><data value='update_table'>update_table</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_tables_py.html#t64">app\api\api_v1\endpoints\tables.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_tables_py.html#t64"><data value='delete_table'>delete_table</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_tables_py.html">app\api\api_v1\endpoints\tables.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_tables_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_workflows_py.html#t15">app\api\api_v1\endpoints\workflows.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_workflows_py.html#t15"><data value='create_workflow'>create_workflow</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_workflows_py.html#t24">app\api\api_v1\endpoints\workflows.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_workflows_py.html#t24"><data value='get_workflows'>get_workflows</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_workflows_py.html#t36">app\api\api_v1\endpoints\workflows.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_workflows_py.html#t36"><data value='get_workflow'>get_workflow</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_workflows_py.html#t51">app\api\api_v1\endpoints\workflows.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_workflows_py.html#t51"><data value='update_workflow'>update_workflow</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_workflows_py.html#t67">app\api\api_v1\endpoints\workflows.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_workflows_py.html#t67"><data value='delete_workflow'>delete_workflow</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_519a1a6647688437_workflows_py.html">app\api\api_v1\endpoints\workflows.py</a></td>
                <td class="name left"><a href="z_519a1a6647688437_workflows_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html">app\core\__init__.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html">app\core\config.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_database_py.html#t23">app\core\database.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_database_py.html#t23"><data value='get_db'>get_db</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_adebc8a9b0574ea2_database_py.html">app\core\database.py</a></td>
                <td class="name left"><a href="z_adebc8a9b0574ea2_database_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html">app\models\__init__.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_generation_py.html">app\models\generation.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_generation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_relationship_py.html">app\models\relationship.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_relationship_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_table_field_py.html">app\models\table_field.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_table_field_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1374716a89f3e08d_workflow_py.html">app\models\workflow.py</a></td>
                <td class="name left"><a href="z_1374716a89f3e08d_workflow_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174___init___py.html">app\schemas\__init__.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_field_py.html">app\schemas\field.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_field_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html">app\schemas\generation.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_generation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_relationship_py.html">app\schemas\relationship.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_relationship_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_table_py.html">app\schemas\table.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_table_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html">app\schemas\workflow.py</a></td>
                <td class="name left"><a href="z_b4c115836e286174_workflow_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html">app\services\__init__.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_deepseek_service_py.html#t17">app\services\deepseek_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_deepseek_service_py.html#t17"><data value='init__'>DeepSeekService.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_deepseek_service_py.html#t25">app\services\deepseek_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_deepseek_service_py.html#t25"><data value='generate_data'>DeepSeekService.generate_data</data></a></td>
                <td>7</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="4 7">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_deepseek_service_py.html#t47">app\services\deepseek_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_deepseek_service_py.html#t47"><data value='build_meta_prompt'>DeepSeekService._build_meta_prompt</data></a></td>
                <td>30</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="29 30">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_deepseek_service_py.html#t113">app\services\deepseek_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_deepseek_service_py.html#t113"><data value='call_deepseek_api'>DeepSeekService._call_deepseek_api</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_deepseek_service_py.html#t143">app\services\deepseek_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_deepseek_service_py.html#t143"><data value='parse_response'>DeepSeekService._parse_response</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_deepseek_service_py.html">app\services\deepseek_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_deepseek_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_field_service_py.html#t14">app\services\field_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_field_service_py.html#t14"><data value='create_field'>FieldService.create_field</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_field_service_py.html#t23">app\services\field_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_field_service_py.html#t23"><data value='get_field'>FieldService.get_field</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_field_service_py.html#t28">app\services\field_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_field_service_py.html#t28"><data value='get_fields_by_table'>FieldService.get_fields_by_table</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_field_service_py.html#t33">app\services\field_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_field_service_py.html#t33"><data value='update_field'>FieldService.update_field</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_field_service_py.html#t48">app\services\field_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_field_service_py.html#t48"><data value='delete_field'>FieldService.delete_field</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_field_service_py.html">app\services\field_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_field_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_table_service_py.html#t14">app\services\table_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_table_service_py.html#t14"><data value='create_table'>TableService.create_table</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_table_service_py.html#t23">app\services\table_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_table_service_py.html#t23"><data value='get_table'>TableService.get_table</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_table_service_py.html#t28">app\services\table_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_table_service_py.html#t28"><data value='get_tables_by_workflow'>TableService.get_tables_by_workflow</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_table_service_py.html#t33">app\services\table_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_table_service_py.html#t33"><data value='update_table'>TableService.update_table</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_table_service_py.html#t48">app\services\table_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_table_service_py.html#t48"><data value='delete_table'>TableService.delete_table</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_table_service_py.html">app\services\table_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_table_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html#t14">app\services\workflow_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html#t14"><data value='create_workflow'>WorkflowService.create_workflow</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html#t23">app\services\workflow_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html#t23"><data value='get_workflow'>WorkflowService.get_workflow</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html#t28">app\services\workflow_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html#t28"><data value='get_workflows'>WorkflowService.get_workflows</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html#t33">app\services\workflow_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html#t33"><data value='get_workflows_count'>WorkflowService.get_workflows_count</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html#t38">app\services\workflow_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html#t38"><data value='update_workflow'>WorkflowService.update_workflow</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html#t53">app\services\workflow_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html#t53"><data value='delete_workflow'>WorkflowService.delete_workflow</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html">app\services\workflow_service.py</a></td>
                <td class="name left"><a href="z_4c37ce8615b5aa70_workflow_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>619</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="576 619">93%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-28 10:33 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
