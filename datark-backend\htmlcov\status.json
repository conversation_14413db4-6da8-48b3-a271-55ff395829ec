{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.1", "globals": "bb1f9c89a822c2bc9e91e2628de991a3", "files": {"z_5f5a17c013354698___init___py": {"hash": "3b541209703044adedea2d97fad0e157", "index": {"url": "z_5f5a17c013354698___init___py.html", "file": "app\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4a9cca768ff3c21b___init___py": {"hash": "9e7942ee108a864ceb6cea108145e925", "index": {"url": "z_4a9cca768ff3c21b___init___py.html", "file": "app\\api\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bb44a478d70e6240___init___py": {"hash": "e8f45252bb8c90bbdee1a0c3b5c86717", "index": {"url": "z_bb44a478d70e6240___init___py.html", "file": "app\\api\\api_v1\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bb44a478d70e6240_api_py": {"hash": "eee60f72ef9e4ce230d2a6d1db829021", "index": {"url": "z_bb44a478d70e6240_api_py.html", "file": "app\\api\\api_v1\\api.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_519a1a6647688437___init___py": {"hash": "98f5f60ac7c241dfae904cf24d357bfd", "index": {"url": "z_519a1a6647688437___init___py.html", "file": "app\\api\\api_v1\\endpoints\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_519a1a6647688437_fields_py": {"hash": "ea4bd6e209542d53ecf695d3a00078d2", "index": {"url": "z_519a1a6647688437_fields_py.html", "file": "app\\api\\api_v1\\endpoints\\fields.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_519a1a6647688437_generation_py": {"hash": "8d725f12d07b7676a676088d518fc265", "index": {"url": "z_519a1a6647688437_generation_py.html", "file": "app\\api\\api_v1\\endpoints\\generation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_519a1a6647688437_tables_py": {"hash": "aedd2d251242a2d9614ea2863f78c73e", "index": {"url": "z_519a1a6647688437_tables_py.html", "file": "app\\api\\api_v1\\endpoints\\tables.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_519a1a6647688437_workflows_py": {"hash": "166032e996dbad85159d1aae0ed95abc", "index": {"url": "z_519a1a6647688437_workflows_py.html", "file": "app\\api\\api_v1\\endpoints\\workflows.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 32, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2___init___py": {"hash": "e27fdb0a5cceb1505eba71ac6cee8a36", "index": {"url": "z_adebc8a9b0574ea2___init___py.html", "file": "app\\core\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_config_py": {"hash": "f0f3c6794cef07737b1c560715569aa3", "index": {"url": "z_adebc8a9b0574ea2_config_py.html", "file": "app\\core\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 15, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_adebc8a9b0574ea2_database_py": {"hash": "deb881605280bb4c4189d9dd0079e925", "index": {"url": "z_adebc8a9b0574ea2_database_py.html", "file": "app\\core\\database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d___init___py": {"hash": "0ac3824cdb6071b75be8aed75b0dbb13", "index": {"url": "z_1374716a89f3e08d___init___py.html", "file": "app\\models\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_generation_py": {"hash": "33e23fe84f1e3b4bb7aa9e7880145a58", "index": {"url": "z_1374716a89f3e08d_generation_py.html", "file": "app\\models\\generation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_relationship_py": {"hash": "579d36d2720667f35abcad8532de07ee", "index": {"url": "z_1374716a89f3e08d_relationship_py.html", "file": "app\\models\\relationship.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_table_field_py": {"hash": "fae188fc450e9e6ea2b2c312b17fa34f", "index": {"url": "z_1374716a89f3e08d_table_field_py.html", "file": "app\\models\\table_field.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_workflow_py": {"hash": "d799000d39b2f0aff916872067ae88f6", "index": {"url": "z_1374716a89f3e08d_workflow_py.html", "file": "app\\models\\workflow.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b4c115836e286174___init___py": {"hash": "9585bee8cde9c5a904eefb6deeb6dff1", "index": {"url": "z_b4c115836e286174___init___py.html", "file": "app\\schemas\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b4c115836e286174_field_py": {"hash": "a8b38261b3dc9f3d37d16d8c139233b3", "index": {"url": "z_b4c115836e286174_field_py.html", "file": "app\\schemas\\field.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b4c115836e286174_generation_py": {"hash": "6f4bdaddbc9f34b9c3dea65510820967", "index": {"url": "z_b4c115836e286174_generation_py.html", "file": "app\\schemas\\generation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b4c115836e286174_relationship_py": {"hash": "830c15f6d3709e8c934edcd872b0cb55", "index": {"url": "z_b4c115836e286174_relationship_py.html", "file": "app\\schemas\\relationship.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b4c115836e286174_table_py": {"hash": "928ae0160a90fe98b9b3855924e9106a", "index": {"url": "z_b4c115836e286174_table_py.html", "file": "app\\schemas\\table.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b4c115836e286174_workflow_py": {"hash": "ed1354e1644f3f7cf97285d031a1db85", "index": {"url": "z_b4c115836e286174_workflow_py.html", "file": "app\\schemas\\workflow.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70___init___py": {"hash": "d0e9717969c8a6466b2097cf7769725b", "index": {"url": "z_4c37ce8615b5aa70___init___py.html", "file": "app\\services\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_deepseek_service_py": {"hash": "5f75492bdfada30782075f85a8a3bf4e", "index": {"url": "z_4c37ce8615b5aa70_deepseek_service_py.html", "file": "app\\services\\deepseek_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_field_service_py": {"hash": "f8cdb3e47ff13edeb2332e56a24c31f0", "index": {"url": "z_4c37ce8615b5aa70_field_service_py.html", "file": "app\\services\\field_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 37, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_table_service_py": {"hash": "191627b8abd758c95a4985df92bafce0", "index": {"url": "z_4c37ce8615b5aa70_table_service_py.html", "file": "app\\services\\table_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 37, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c37ce8615b5aa70_workflow_service_py": {"hash": "3b7ed715ea60b0172036f9daad72f1b3", "index": {"url": "z_4c37ce8615b5aa70_workflow_service_py.html", "file": "app\\services\\workflow_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}