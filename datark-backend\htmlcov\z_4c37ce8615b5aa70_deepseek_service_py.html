<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for app\services\deepseek_service.py: 77%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>app\services\deepseek_service.py</b>:
            <span class="pc_cov">77%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">70 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">54<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">16<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_4c37ce8615b5aa70___init___py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_4c37ce8615b5aa70_field_service_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-28 10:33 +0800
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">DeepSeek AI service for data generation</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="key">import</span> <span class="nam">json</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">import</span> <span class="nam">httpx</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">Dict</span><span class="op">,</span> <span class="nam">Any</span><span class="op">,</span> <span class="nam">List</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">from</span> <span class="nam">sqlalchemy</span><span class="op">.</span><span class="nam">orm</span> <span class="key">import</span> <span class="nam">Session</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">from</span> <span class="nam">app</span><span class="op">.</span><span class="nam">core</span><span class="op">.</span><span class="nam">config</span> <span class="key">import</span> <span class="nam">settings</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">from</span> <span class="nam">app</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">workflow</span> <span class="key">import</span> <span class="nam">Workflow</span><span class="op">,</span> <span class="nam">DataTable</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="key">from</span> <span class="nam">app</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">table_field</span> <span class="key">import</span> <span class="nam">TableField</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="key">from</span> <span class="nam">app</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">relationship</span> <span class="key">import</span> <span class="nam">TableRelationship</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t"><span class="key">class</span> <span class="nam">DeepSeekService</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">    <span class="str">"""Service for DeepSeek AI integration"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">    <span class="key">def</span> <span class="nam">__init__</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">api_key</span> <span class="op">=</span> <span class="nam">settings</span><span class="op">.</span><span class="nam">DEEPSEEK_API_KEY</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">api_url</span> <span class="op">=</span> <span class="nam">settings</span><span class="op">.</span><span class="nam">DEEPSEEK_API_URL</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">headers</span> <span class="op">=</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">            <span class="str">"Authorization"</span><span class="op">:</span> <span class="fst">f"</span><span class="fst">Bearer </span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">api_key</span><span class="op">}</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">            <span class="str">"Content-Type"</span><span class="op">:</span> <span class="str">"application/json"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">generate_data</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">db</span><span class="op">:</span> <span class="nam">Session</span><span class="op">,</span> <span class="nam">workflow_id</span><span class="op">:</span> <span class="nam">int</span><span class="op">,</span> <span class="nam">record_counts</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">int</span><span class="op">]</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Any</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">        <span class="str">"""Generate data for a workflow"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">        <span class="com"># Get workflow with all related data</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">        <span class="nam">workflow</span> <span class="op">=</span> <span class="nam">db</span><span class="op">.</span><span class="nam">query</span><span class="op">(</span><span class="nam">Workflow</span><span class="op">)</span><span class="op">.</span><span class="nam">filter</span><span class="op">(</span><span class="nam">Workflow</span><span class="op">.</span><span class="nam">id</span> <span class="op">==</span> <span class="nam">workflow_id</span><span class="op">)</span><span class="op">.</span><span class="nam">first</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">workflow</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Workflow </span><span class="op">{</span><span class="nam">workflow_id</span><span class="op">}</span><span class="fst"> not found</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">        <span class="com"># Build meta prompt</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">        <span class="nam">meta_prompt</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_build_meta_prompt</span><span class="op">(</span><span class="nam">db</span><span class="op">,</span> <span class="nam">workflow</span><span class="op">,</span> <span class="nam">record_counts</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">        <span class="com"># Call DeepSeek API</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">        <span class="nam">response_data</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_call_deepseek_api</span><span class="op">(</span><span class="nam">meta_prompt</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">        <span class="com"># Parse and validate response</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">        <span class="nam">generated_data</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_parse_response</span><span class="op">(</span><span class="nam">response_data</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">        <span class="key">return</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">            <span class="str">"meta_prompt"</span><span class="op">:</span> <span class="nam">meta_prompt</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">            <span class="str">"ai_response"</span><span class="op">:</span> <span class="nam">response_data</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">            <span class="str">"generated_data"</span><span class="op">:</span> <span class="nam">generated_data</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis mis2 show_mis"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">    <span class="key">def</span> <span class="nam">_build_meta_prompt</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">db</span><span class="op">:</span> <span class="nam">Session</span><span class="op">,</span> <span class="nam">workflow</span><span class="op">:</span> <span class="nam">Workflow</span><span class="op">,</span> <span class="nam">record_counts</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">int</span><span class="op">]</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">        <span class="str">"""Build meta prompt for AI"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">        <span class="com"># Get all tables with fields and relationships</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">        <span class="nam">tables</span> <span class="op">=</span> <span class="nam">db</span><span class="op">.</span><span class="nam">query</span><span class="op">(</span><span class="nam">DataTable</span><span class="op">)</span><span class="op">.</span><span class="nam">filter</span><span class="op">(</span><span class="nam">DataTable</span><span class="op">.</span><span class="nam">workflow_id</span> <span class="op">==</span> <span class="nam">workflow</span><span class="op">.</span><span class="nam">id</span><span class="op">)</span><span class="op">.</span><span class="nam">all</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">        <span class="nam">relationships</span> <span class="op">=</span> <span class="nam">db</span><span class="op">.</span><span class="nam">query</span><span class="op">(</span><span class="nam">TableRelationship</span><span class="op">)</span><span class="op">.</span><span class="nam">filter</span><span class="op">(</span><span class="nam">TableRelationship</span><span class="op">.</span><span class="nam">workflow_id</span> <span class="op">==</span> <span class="nam">workflow</span><span class="op">.</span><span class="nam">id</span><span class="op">)</span><span class="op">.</span><span class="nam">all</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">        <span class="nam">prompt_parts</span> <span class="op">=</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">            <span class="fst">f"</span><span class="fst"># &#25968;&#25454;&#29983;&#25104;&#20219;&#21153;&#65306;</span><span class="op">{</span><span class="nam">workflow</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">            <span class="fst">f"</span><span class="fst">&#25551;&#36848;&#65306;</span><span class="op">{</span><span class="nam">workflow</span><span class="op">.</span><span class="nam">description</span> <span class="key">or</span> <span class="str">'&#26080;&#25551;&#36848;'</span><span class="op">}</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">            <span class="str">""</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">            <span class="str">"## &#25968;&#25454;&#34920;&#32467;&#26500;&#23450;&#20041;"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">        <span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">        <span class="com"># Add table definitions</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">        <span class="key">for</span> <span class="nam">table</span> <span class="key">in</span> <span class="nam">tables</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">            <span class="nam">fields</span> <span class="op">=</span> <span class="nam">db</span><span class="op">.</span><span class="nam">query</span><span class="op">(</span><span class="nam">TableField</span><span class="op">)</span><span class="op">.</span><span class="nam">filter</span><span class="op">(</span><span class="nam">TableField</span><span class="op">.</span><span class="nam">table_id</span> <span class="op">==</span> <span class="nam">table</span><span class="op">.</span><span class="nam">id</span><span class="op">)</span><span class="op">.</span><span class="nam">order_by</span><span class="op">(</span><span class="nam">TableField</span><span class="op">.</span><span class="nam">order_index</span><span class="op">)</span><span class="op">.</span><span class="nam">all</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">            <span class="nam">prompt_parts</span><span class="op">.</span><span class="nam">extend</span><span class="op">(</span><span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">                <span class="fst">f"</span><span class="fst">### &#34920;&#65306;</span><span class="op">{</span><span class="nam">table</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst"> (</span><span class="op">{</span><span class="nam">table</span><span class="op">.</span><span class="nam">alias</span> <span class="key">or</span> <span class="nam">table</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst">)</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">                <span class="fst">f"</span><span class="fst">&#38656;&#35201;&#29983;&#25104;&#35760;&#24405;&#25968;&#65306;</span><span class="op">{</span><span class="nam">record_counts</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="nam">table</span><span class="op">.</span><span class="nam">name</span><span class="op">,</span> <span class="num">10</span><span class="op">)</span><span class="op">}</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">                <span class="str">"&#23383;&#27573;&#23450;&#20041;&#65306;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">            <span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">            <span class="key">for</span> <span class="nam">field</span> <span class="key">in</span> <span class="nam">fields</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">                <span class="nam">field_desc</span> <span class="op">=</span> <span class="fst">f"</span><span class="fst">- </span><span class="op">{</span><span class="nam">field</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst"> (</span><span class="op">{</span><span class="nam">field</span><span class="op">.</span><span class="nam">field_type</span><span class="op">.</span><span class="nam">value</span><span class="op">}</span><span class="fst">)</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">                <span class="key">if</span> <span class="nam">field</span><span class="op">.</span><span class="nam">ai_prompt</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">                    <span class="nam">field_desc</span> <span class="op">+=</span> <span class="fst">f"</span><span class="fst">: </span><span class="op">{</span><span class="nam">field</span><span class="op">.</span><span class="nam">ai_prompt</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">                <span class="key">if</span> <span class="nam">field</span><span class="op">.</span><span class="nam">is_required</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">                    <span class="nam">field_desc</span> <span class="op">+=</span> <span class="str">" [&#24517;&#22635;]"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">                <span class="key">if</span> <span class="nam">field</span><span class="op">.</span><span class="nam">is_unique</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">                    <span class="nam">field_desc</span> <span class="op">+=</span> <span class="str">" [&#21807;&#19968;]"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">                <span class="key">if</span> <span class="nam">field</span><span class="op">.</span><span class="nam">is_primary_key</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">                    <span class="nam">field_desc</span> <span class="op">+=</span> <span class="str">" [&#20027;&#38190;]"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">                <span class="nam">prompt_parts</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">field_desc</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">            <span class="nam">prompt_parts</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">""</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">        <span class="com"># Add relationships</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">        <span class="key">if</span> <span class="nam">relationships</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">            <span class="nam">prompt_parts</span><span class="op">.</span><span class="nam">extend</span><span class="op">(</span><span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">                <span class="str">"## &#34920;&#20851;&#31995;&#23450;&#20041;"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">            <span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">            <span class="key">for</span> <span class="nam">rel</span> <span class="key">in</span> <span class="nam">relationships</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">                <span class="nam">source_table</span> <span class="op">=</span> <span class="nam">next</span><span class="op">(</span><span class="op">(</span><span class="nam">t</span> <span class="key">for</span> <span class="nam">t</span> <span class="key">in</span> <span class="nam">tables</span> <span class="key">if</span> <span class="nam">t</span><span class="op">.</span><span class="nam">id</span> <span class="op">==</span> <span class="nam">rel</span><span class="op">.</span><span class="nam">source_table_id</span><span class="op">)</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">                <span class="nam">target_table</span> <span class="op">=</span> <span class="nam">next</span><span class="op">(</span><span class="op">(</span><span class="nam">t</span> <span class="key">for</span> <span class="nam">t</span> <span class="key">in</span> <span class="nam">tables</span> <span class="key">if</span> <span class="nam">t</span><span class="op">.</span><span class="nam">id</span> <span class="op">==</span> <span class="nam">rel</span><span class="op">.</span><span class="nam">target_table_id</span><span class="op">)</span><span class="op">,</span> <span class="key">None</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">                <span class="key">if</span> <span class="nam">source_table</span> <span class="key">and</span> <span class="nam">target_table</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">                    <span class="nam">rel_desc</span> <span class="op">=</span> <span class="fst">f"</span><span class="fst">- </span><span class="op">{</span><span class="nam">source_table</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst"> -> </span><span class="op">{</span><span class="nam">target_table</span><span class="op">.</span><span class="nam">name</span><span class="op">}</span><span class="fst"> (</span><span class="op">{</span><span class="nam">rel</span><span class="op">.</span><span class="nam">relationship_type</span><span class="op">.</span><span class="nam">value</span><span class="op">}</span><span class="fst">)</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">                    <span class="key">if</span> <span class="nam">rel</span><span class="op">.</span><span class="nam">description</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">                        <span class="nam">rel_desc</span> <span class="op">+=</span> <span class="fst">f"</span><span class="fst">: </span><span class="op">{</span><span class="nam">rel</span><span class="op">.</span><span class="nam">description</span><span class="op">}</span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">                    <span class="nam">prompt_parts</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="nam">rel_desc</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">        <span class="com"># Add generation instructions</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">        <span class="nam">prompt_parts</span><span class="op">.</span><span class="nam">extend</span><span class="op">(</span><span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">            <span class="str">""</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">            <span class="str">"## &#29983;&#25104;&#35201;&#27714;"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">            <span class="str">"1. &#35831;&#26681;&#25454;&#19978;&#36848;&#34920;&#32467;&#26500;&#21644;&#20851;&#31995;&#29983;&#25104;&#31526;&#21512;&#36923;&#36753;&#30340;&#27169;&#25311;&#25968;&#25454;"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">            <span class="str">"2. &#30830;&#20445;&#20851;&#31995;&#25968;&#25454;&#30340;&#19968;&#33268;&#24615;&#65288;&#22914;&#22806;&#38190;&#24341;&#29992;&#27491;&#30830;&#65289;"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">            <span class="str">"3. &#29983;&#25104;&#30340;&#25968;&#25454;&#24212;&#35813;&#30495;&#23454;&#12289;&#21512;&#29702;&#65292;&#31526;&#21512;&#19994;&#21153;&#22330;&#26223;"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">            <span class="str">"4. &#36820;&#22238;JSON&#26684;&#24335;&#65292;&#32467;&#26500;&#20026;&#65306;{\"table_name\": [{\"field\": \"value\", ...}, ...]}"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">            <span class="str">"5. &#35831;&#30830;&#20445;JSON&#26684;&#24335;&#27491;&#30830;&#65292;&#21487;&#20197;&#34987;&#35299;&#26512;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">        <span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">        <span class="key">return</span> <span class="str">"\n"</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">prompt_parts</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">    <span class="key">async</span> <span class="key">def</span> <span class="nam">_call_deepseek_api</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">prompt</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">str</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">        <span class="str">"""Call DeepSeek API"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">        <span class="nam">payload</span> <span class="op">=</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">            <span class="str">"model"</span><span class="op">:</span> <span class="str">"deepseek-chat"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">            <span class="str">"messages"</span><span class="op">:</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">                <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">                    <span class="str">"role"</span><span class="op">:</span> <span class="str">"system"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">                    <span class="str">"content"</span><span class="op">:</span> <span class="str">"&#20320;&#26159;&#19968;&#20010;&#19987;&#19994;&#30340;&#25968;&#25454;&#29983;&#25104;&#21161;&#25163;&#65292;&#33021;&#22815;&#26681;&#25454;&#25968;&#25454;&#24211;&#34920;&#32467;&#26500;&#29983;&#25104;&#39640;&#36136;&#37327;&#30340;&#27169;&#25311;&#25968;&#25454;&#12290;"</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">                <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">                <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">                    <span class="str">"role"</span><span class="op">:</span> <span class="str">"user"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">                    <span class="str">"content"</span><span class="op">:</span> <span class="nam">prompt</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">                <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">            <span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">            <span class="str">"temperature"</span><span class="op">:</span> <span class="num">0.7</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">            <span class="str">"max_tokens"</span><span class="op">:</span> <span class="num">4000</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">        <span class="key">async</span> <span class="key">with</span> <span class="nam">httpx</span><span class="op">.</span><span class="nam">AsyncClient</span><span class="op">(</span><span class="op">)</span> <span class="key">as</span> <span class="nam">client</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">            <span class="nam">response</span> <span class="op">=</span> <span class="key">await</span> <span class="nam">client</span><span class="op">.</span><span class="nam">post</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">                <span class="fst">f"</span><span class="op">{</span><span class="nam">self</span><span class="op">.</span><span class="nam">api_url</span><span class="op">}</span><span class="fst">/chat/completions</span><span class="fst">"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t">                <span class="nam">headers</span><span class="op">=</span><span class="nam">self</span><span class="op">.</span><span class="nam">headers</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">                <span class="nam">json</span><span class="op">=</span><span class="nam">payload</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">                <span class="nam">timeout</span><span class="op">=</span><span class="num">60.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="run run2"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">            <span class="nam">response</span><span class="op">.</span><span class="nam">raise_for_status</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">            <span class="nam">result</span> <span class="op">=</span> <span class="nam">response</span><span class="op">.</span><span class="nam">json</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">            <span class="key">return</span> <span class="nam">result</span><span class="op">[</span><span class="str">"choices"</span><span class="op">]</span><span class="op">[</span><span class="num">0</span><span class="op">]</span><span class="op">[</span><span class="str">"message"</span><span class="op">]</span><span class="op">[</span><span class="str">"content"</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">    <span class="key">def</span> <span class="nam">_parse_response</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">response_text</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">List</span><span class="op">[</span><span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Any</span><span class="op">]</span><span class="op">]</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">        <span class="str">"""Parse AI response to extract JSON data"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">            <span class="com"># Try to find JSON in the response</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">            <span class="nam">start_idx</span> <span class="op">=</span> <span class="nam">response_text</span><span class="op">.</span><span class="nam">find</span><span class="op">(</span><span class="str">'{'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">            <span class="nam">end_idx</span> <span class="op">=</span> <span class="nam">response_text</span><span class="op">.</span><span class="nam">rfind</span><span class="op">(</span><span class="str">'}'</span><span class="op">)</span> <span class="op">+</span> <span class="num">1</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">            <span class="key">if</span> <span class="nam">start_idx</span> <span class="op">==</span> <span class="op">-</span><span class="num">1</span> <span class="key">or</span> <span class="nam">end_idx</span> <span class="op">==</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">                <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">"No JSON found in response"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">            <span class="nam">json_str</span> <span class="op">=</span> <span class="nam">response_text</span><span class="op">[</span><span class="nam">start_idx</span><span class="op">:</span><span class="nam">end_idx</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">            <span class="nam">data</span> <span class="op">=</span> <span class="nam">json</span><span class="op">.</span><span class="nam">loads</span><span class="op">(</span><span class="nam">json_str</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">            <span class="key">return</span> <span class="nam">data</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">        <span class="key">except</span> <span class="op">(</span><span class="nam">json</span><span class="op">.</span><span class="nam">JSONDecodeError</span><span class="op">,</span> <span class="nam">ValueError</span><span class="op">)</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="fst">f"</span><span class="fst">Failed to parse AI response: </span><span class="op">{</span><span class="nam">str</span><span class="op">(</span><span class="nam">e</span><span class="op">)</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_4c37ce8615b5aa70___init___py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_4c37ce8615b5aa70_field_service_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-28 10:33 +0800
        </p>
    </div>
</footer>
</body>
</html>
