"""
Tests for table field API endpoints
"""
import pytest
from fastapi.testclient import <PERSON><PERSON>lient
from sqlalchemy.orm import Session

from app.models.workflow import DataTable
from app.models.table_field import TableField, FieldType


class TestFieldEndpoints:
    """Test table field CRUD endpoints"""
    
    def test_create_field_success(self, client: TestClient, sample_table: DataTable):
        """Test successful field creation"""
        field_data = {
            "name": "email",
            "field_type": FieldType.EMAIL.value,
            "ai_prompt": "Generate a realistic email address",
            "is_required": True,
            "is_unique": True,
            "is_primary_key": False,
            "table_id": sample_table.id,
            "order_index": 2
        }
        
        response = client.post("/api/v1/fields/", json=field_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == field_data["name"]
        assert data["field_type"] == field_data["field_type"]
        assert data["ai_prompt"] == field_data["ai_prompt"]
        assert data["is_required"] == field_data["is_required"]
        assert data["is_unique"] == field_data["is_unique"]
        assert data["is_primary_key"] == field_data["is_primary_key"]
        assert data["table_id"] == field_data["table_id"]
        assert data["order_index"] == field_data["order_index"]
        assert "id" in data
        assert "created_at" in data
    
    def test_create_field_minimal_data(self, client: TestClient, sample_table: DataTable):
        """Test field creation with minimal required data"""
        field_data = {
            "name": "id",
            "field_type": FieldType.NUMBER.value,
            "table_id": sample_table.id
        }
        
        response = client.post("/api/v1/fields/", json=field_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == field_data["name"]
        assert data["field_type"] == field_data["field_type"]
        assert data["table_id"] == field_data["table_id"]
        assert data["ai_prompt"] is None
        assert data["is_required"] is False
        assert data["is_unique"] is False
        assert data["is_primary_key"] is False
        assert data["order_index"] == 0
    
    def test_create_field_with_enum_options(self, client: TestClient, sample_table: DataTable):
        """Test field creation with enum options"""
        field_data = {
            "name": "status",
            "field_type": FieldType.ENUM.value,
            "enum_options": '["active", "inactive", "pending"]',
            "table_id": sample_table.id
        }
        
        response = client.post("/api/v1/fields/", json=field_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == field_data["name"]
        assert data["field_type"] == field_data["field_type"]
        assert data["enum_options"] == field_data["enum_options"]
    
    def test_create_field_with_default_value(self, client: TestClient, sample_table: DataTable):
        """Test field creation with default value"""
        field_data = {
            "name": "is_active",
            "field_type": FieldType.BOOLEAN.value,
            "default_value": "true",
            "table_id": sample_table.id
        }
        
        response = client.post("/api/v1/fields/", json=field_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == field_data["name"]
        assert data["default_value"] == field_data["default_value"]
    
    def test_create_field_invalid_data(self, client: TestClient, sample_table: DataTable):
        """Test field creation with invalid data"""
        # Empty name
        response = client.post("/api/v1/fields/", json={
            "name": "",
            "field_type": FieldType.TEXT.value,
            "table_id": sample_table.id
        })
        assert response.status_code == 422
        
        # Missing name
        response = client.post("/api/v1/fields/", json={
            "field_type": FieldType.TEXT.value,
            "table_id": sample_table.id
        })
        assert response.status_code == 422
        
        # Missing field_type
        response = client.post("/api/v1/fields/", json={
            "name": "test_field",
            "table_id": sample_table.id
        })
        assert response.status_code == 422
        
        # Missing table_id
        response = client.post("/api/v1/fields/", json={
            "name": "test_field",
            "field_type": FieldType.TEXT.value
        })
        assert response.status_code == 422
        
        # Invalid field_type
        response = client.post("/api/v1/fields/", json={
            "name": "test_field",
            "field_type": "invalid_type",
            "table_id": sample_table.id
        })
        assert response.status_code == 422
        
        # Name too long
        long_name = "x" * 256
        response = client.post("/api/v1/fields/", json={
            "name": long_name,
            "field_type": FieldType.TEXT.value,
            "table_id": sample_table.id
        })
        assert response.status_code == 422
    
    def test_create_field_invalid_table_id(self, client: TestClient):
        """Test field creation with non-existent table ID"""
        field_data = {
            "name": "test_field",
            "field_type": FieldType.TEXT.value,
            "table_id": 999
        }

        response = client.post("/api/v1/fields/", json=field_data)
        # SQLite in-memory database might not enforce foreign key constraints
        # So this test might pass with 201, or fail with 400/422/500
        assert response.status_code in [201, 400, 422, 500]
    
    def test_get_fields_by_table_empty(self, client: TestClient, sample_table: DataTable):
        """Test getting fields for table with no fields"""
        response = client.get(f"/api/v1/fields/table/{sample_table.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data == []
    
    def test_get_fields_by_table_with_data(self, client: TestClient, sample_field: TableField):
        """Test getting fields for table with existing fields"""
        response = client.get(f"/api/v1/fields/table/{sample_field.table_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["id"] == sample_field.id
        assert data[0]["name"] == sample_field.name
        assert data[0]["table_id"] == sample_field.table_id
    
    def test_get_fields_by_table_multiple_fields(self, client: TestClient, sample_table: DataTable, db_session: Session):
        """Test getting multiple fields for a table"""
        # Create multiple fields
        fields = []
        field_types = [FieldType.NUMBER, FieldType.TEXT, FieldType.EMAIL, FieldType.DATE]
        for i, field_type in enumerate(field_types):
            field = TableField(
                name=f"field_{i}",
                field_type=field_type,
                table_id=sample_table.id,
                order_index=i
            )
            db_session.add(field)
            fields.append(field)
        db_session.commit()
        
        response = client.get(f"/api/v1/fields/table/{sample_table.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 4
        
        # Check all fields are returned
        returned_names = {field["name"] for field in data}
        expected_names = {f"field_{i}" for i in range(4)}
        assert returned_names == expected_names
        
        # Check order is preserved
        sorted_data = sorted(data, key=lambda x: x["order_index"])
        for i, field_data in enumerate(sorted_data):
            assert field_data["name"] == f"field_{i}"
            assert field_data["order_index"] == i
    
    def test_get_fields_by_table_not_found(self, client: TestClient):
        """Test getting fields for non-existent table"""
        response = client.get("/api/v1/fields/table/999")
        
        assert response.status_code == 200
        data = response.json()
        assert data == []
    
    def test_get_field_by_id_success(self, client: TestClient, sample_field: TableField):
        """Test getting field by ID successfully"""
        response = client.get(f"/api/v1/fields/{sample_field.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == sample_field.id
        assert data["name"] == sample_field.name
        assert data["field_type"] == sample_field.field_type.value
        assert data["ai_prompt"] == sample_field.ai_prompt
        assert data["is_required"] == sample_field.is_required
        assert data["is_unique"] == sample_field.is_unique
        assert data["is_primary_key"] == sample_field.is_primary_key
        assert data["table_id"] == sample_field.table_id
        assert data["order_index"] == sample_field.order_index
    
    def test_get_field_by_id_not_found(self, client: TestClient):
        """Test getting field by non-existent ID"""
        response = client.get("/api/v1/fields/999")
        
        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "Field not found"
    
    def test_get_field_by_id_invalid_id(self, client: TestClient):
        """Test getting field with invalid ID format"""
        response = client.get("/api/v1/fields/invalid")

        assert response.status_code == 422

    def test_update_field_success(self, client: TestClient, sample_field: TableField):
        """Test successful field update"""
        update_data = {
            "name": "updated_username",
            "field_type": FieldType.EMAIL.value,
            "ai_prompt": "Generate updated email",
            "is_required": False,
            "is_unique": False,
            "is_primary_key": True,
            "order_index": 5
        }

        response = client.put(f"/api/v1/fields/{sample_field.id}", json=update_data)

        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["field_type"] == update_data["field_type"]
        assert data["ai_prompt"] == update_data["ai_prompt"]
        assert data["is_required"] == update_data["is_required"]
        assert data["is_unique"] == update_data["is_unique"]
        assert data["is_primary_key"] == update_data["is_primary_key"]
        assert data["order_index"] == update_data["order_index"]
        assert data["id"] == sample_field.id
        assert data["table_id"] == sample_field.table_id  # Unchanged

    def test_update_field_partial(self, client: TestClient, sample_field: TableField):
        """Test partial field update"""
        update_data = {"name": "partially_updated"}

        response = client.put(f"/api/v1/fields/{sample_field.id}", json=update_data)

        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["field_type"] == sample_field.field_type.value  # Unchanged
        assert data["ai_prompt"] == sample_field.ai_prompt  # Unchanged
        assert data["is_required"] == sample_field.is_required  # Unchanged

    def test_update_field_enum_options(self, client: TestClient, sample_field: TableField):
        """Test updating field with enum options"""
        update_data = {
            "field_type": FieldType.ENUM.value,
            "enum_options": '["option1", "option2", "option3"]'
        }

        response = client.put(f"/api/v1/fields/{sample_field.id}", json=update_data)

        assert response.status_code == 200
        data = response.json()
        assert data["field_type"] == update_data["field_type"]
        assert data["enum_options"] == update_data["enum_options"]

    def test_update_field_default_value(self, client: TestClient, sample_field: TableField):
        """Test updating field with default value"""
        update_data = {
            "field_type": FieldType.BOOLEAN.value,
            "default_value": "false"
        }

        response = client.put(f"/api/v1/fields/{sample_field.id}", json=update_data)

        assert response.status_code == 200
        data = response.json()
        assert data["field_type"] == update_data["field_type"]
        assert data["default_value"] == update_data["default_value"]

    def test_update_field_not_found(self, client: TestClient):
        """Test updating non-existent field"""
        update_data = {"name": "non_existent"}

        response = client.put("/api/v1/fields/999", json=update_data)

        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "Field not found"

    def test_update_field_invalid_data(self, client: TestClient, sample_field: TableField):
        """Test updating field with invalid data"""
        # Empty name
        response = client.put(f"/api/v1/fields/{sample_field.id}", json={"name": ""})
        assert response.status_code == 422

        # Name too long
        long_name = "x" * 256
        response = client.put(f"/api/v1/fields/{sample_field.id}", json={"name": long_name})
        assert response.status_code == 422

        # Invalid field_type
        response = client.put(f"/api/v1/fields/{sample_field.id}", json={"field_type": "invalid_type"})
        assert response.status_code == 422

    def test_delete_field_success(self, client: TestClient, sample_field: TableField):
        """Test successful field deletion"""
        response = client.delete(f"/api/v1/fields/{sample_field.id}")

        assert response.status_code == 204

        # Verify field is deleted
        get_response = client.get(f"/api/v1/fields/{sample_field.id}")
        assert get_response.status_code == 404

    def test_delete_field_not_found(self, client: TestClient):
        """Test deleting non-existent field"""
        response = client.delete("/api/v1/fields/999")

        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "Field not found"

    def test_delete_field_invalid_id(self, client: TestClient):
        """Test deleting field with invalid ID format"""
        response = client.delete("/api/v1/fields/invalid")

        assert response.status_code == 422
