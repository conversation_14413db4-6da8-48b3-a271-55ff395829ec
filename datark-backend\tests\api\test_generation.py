"""
Tests for data generation API endpoints
"""
import pytest
from unittest.mock import AsyncMock, patch
from fastapi.testclient import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session

from app.models.workflow import Workflow, WorkflowStatus
from app.models.generation import GenerationTask, GenerationStatus


class TestGenerationEndpoints:
    """Test data generation endpoints"""
    
    @patch('app.services.deepseek_service.DeepSeekService.generate_data')
    def test_generate_data_success(self, mock_generate_data, client: TestClient, sample_workflow_with_tables: Workflow):
        """Test successful data generation start"""
        # Mock the DeepSeek service response
        mock_generate_data.return_value = {
            "meta_prompt": "Test prompt",
            "ai_response": "Test AI response",
            "generated_data": {"users": [{"id": 1, "username": "test_user"}]}
        }
        
        generation_request = {
            "record_counts": {"users": 10, "posts": 50}
        }
        
        response = client.post(
            f"/api/v1/generation/workflows/{sample_workflow_with_tables.id}/generate",
            json=generation_request
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "task_id" in data
        assert data["status"] == GenerationStatus.PENDING.value
        assert isinstance(data["task_id"], int)
    
    def test_generate_data_workflow_not_found(self, client: TestClient):
        """Test data generation with non-existent workflow"""
        generation_request = {
            "record_counts": {"users": 10}
        }
        
        response = client.post(
            "/api/v1/generation/workflows/999/generate",
            json=generation_request
        )
        
        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "Workflow not found"
    
    def test_generate_data_invalid_request(self, client: TestClient, sample_workflow_with_tables: Workflow):
        """Test data generation with invalid request data"""
        # Missing record_counts
        response = client.post(
            f"/api/v1/generation/workflows/{sample_workflow_with_tables.id}/generate",
            json={}
        )
        assert response.status_code == 422
        
        # Invalid record_counts format
        response = client.post(
            f"/api/v1/generation/workflows/{sample_workflow_with_tables.id}/generate",
            json={"record_counts": "invalid"}
        )
        assert response.status_code == 422
        
        # Empty record_counts
        response = client.post(
            f"/api/v1/generation/workflows/{sample_workflow_with_tables.id}/generate",
            json={"record_counts": {}}
        )
        assert response.status_code == 200  # This might be valid, depending on business logic
    
    def test_generate_data_invalid_workflow_id(self, client: TestClient):
        """Test data generation with invalid workflow ID format"""
        generation_request = {
            "record_counts": {"users": 10}
        }
        
        response = client.post(
            "/api/v1/generation/workflows/invalid/generate",
            json=generation_request
        )
        
        assert response.status_code == 422
    
    def test_generate_data_negative_record_counts(self, client: TestClient, sample_workflow_with_tables: Workflow):
        """Test data generation with negative record counts"""
        generation_request = {
            "record_counts": {"users": -5, "posts": 10}
        }
        
        response = client.post(
            f"/api/v1/generation/workflows/{sample_workflow_with_tables.id}/generate",
            json=generation_request
        )
        
        # This should be handled by business logic validation
        # For now, we'll accept it as the API might not validate this
        assert response.status_code in [200, 422]
    
    def test_generate_data_zero_record_counts(self, client: TestClient, sample_workflow_with_tables: Workflow):
        """Test data generation with zero record counts"""
        generation_request = {
            "record_counts": {"users": 0, "posts": 0}
        }
        
        response = client.post(
            f"/api/v1/generation/workflows/{sample_workflow_with_tables.id}/generate",
            json=generation_request
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "task_id" in data
        assert data["status"] == GenerationStatus.PENDING.value
    
    def test_generate_data_large_record_counts(self, client: TestClient, sample_workflow_with_tables: Workflow):
        """Test data generation with large record counts"""
        generation_request = {
            "record_counts": {"users": 1000000, "posts": 5000000}
        }
        
        response = client.post(
            f"/api/v1/generation/workflows/{sample_workflow_with_tables.id}/generate",
            json=generation_request
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "task_id" in data
        assert data["status"] == GenerationStatus.PENDING.value
    
    def test_get_generation_task_success(self, client: TestClient, sample_generation_task: GenerationTask):
        """Test getting generation task successfully"""
        response = client.get(f"/api/v1/generation/tasks/{sample_generation_task.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == sample_generation_task.id
        assert data["workflow_id"] == sample_generation_task.workflow_id
        assert data["status"] == sample_generation_task.status.value
        assert data["record_counts"] == sample_generation_task.record_counts
    
    def test_get_generation_task_not_found(self, client: TestClient):
        """Test getting non-existent generation task"""
        response = client.get("/api/v1/generation/tasks/999")
        
        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "Generation task not found"
    
    def test_get_generation_task_invalid_id(self, client: TestClient):
        """Test getting generation task with invalid ID format"""
        response = client.get("/api/v1/generation/tasks/invalid")
        
        assert response.status_code == 422
    
    def test_get_generation_task_completed(self, client: TestClient, db_session: Session, sample_workflow: Workflow):
        """Test getting completed generation task with results"""
        # Create a completed task
        task = GenerationTask(
            workflow_id=sample_workflow.id,
            status=GenerationStatus.COMPLETED,
            record_counts={"users": 5},
            generated_data={"users": [{"id": 1, "username": "test"}]},
            meta_prompt="Test prompt",
            ai_response="Test AI response"
        )
        db_session.add(task)
        db_session.commit()
        db_session.refresh(task)
        
        response = client.get(f"/api/v1/generation/tasks/{task.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == GenerationStatus.COMPLETED.value
        assert data["generated_data"] == {"users": [{"id": 1, "username": "test"}]}
        assert data["meta_prompt"] == "Test prompt"
        assert data["ai_response"] == "Test AI response"
    
    def test_get_generation_task_failed(self, client: TestClient, db_session: Session, sample_workflow: Workflow):
        """Test getting failed generation task with error message"""
        # Create a failed task
        task = GenerationTask(
            workflow_id=sample_workflow.id,
            status=GenerationStatus.FAILED,
            record_counts={"users": 5},
            error_message="DeepSeek API error: Rate limit exceeded"
        )
        db_session.add(task)
        db_session.commit()
        db_session.refresh(task)
        
        response = client.get(f"/api/v1/generation/tasks/{task.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == GenerationStatus.FAILED.value
        assert data["error_message"] == "DeepSeek API error: Rate limit exceeded"
        assert data["generated_data"] is None
    
    def test_get_generation_task_running(self, client: TestClient, db_session: Session, sample_workflow: Workflow):
        """Test getting running generation task"""
        # Create a running task
        task = GenerationTask(
            workflow_id=sample_workflow.id,
            status=GenerationStatus.RUNNING,
            record_counts={"users": 100}
        )
        db_session.add(task)
        db_session.commit()
        db_session.refresh(task)
        
        response = client.get(f"/api/v1/generation/tasks/{task.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == GenerationStatus.RUNNING.value
        assert data["generated_data"] is None
        assert data["error_message"] is None

    @patch('app.services.deepseek_service.DeepSeekService.generate_data')
    def test_generation_workflow_status_update(self, mock_generate_data, client: TestClient, db_session: Session, sample_workflow_with_tables: Workflow):
        """Test that workflow status is updated during generation"""
        # Mock the DeepSeek service response
        mock_generate_data.return_value = {
            "meta_prompt": "Test prompt",
            "ai_response": "Test AI response",
            "generated_data": {"users": [{"id": 1, "username": "test_user"}]}
        }

        # Check initial workflow status
        assert sample_workflow_with_tables.status == WorkflowStatus.DRAFT

        generation_request = {
            "record_counts": {"users": 5}
        }

        response = client.post(
            f"/api/v1/generation/workflows/{sample_workflow_with_tables.id}/generate",
            json=generation_request
        )

        assert response.status_code == 200

        # Refresh workflow from database
        db_session.refresh(sample_workflow_with_tables)

        # Workflow status might be updated to RUNNING or COMPLETED depending on implementation
        # Since this is a mock test, the generation might complete immediately
        assert sample_workflow_with_tables.status in [WorkflowStatus.RUNNING, WorkflowStatus.COMPLETED]

    @patch('app.services.deepseek_service.DeepSeekService.generate_data')
    def test_generation_with_unknown_table_names(self, mock_generate_data, client: TestClient, sample_workflow_with_tables: Workflow):
        """Test data generation with table names not in workflow"""
        mock_generate_data.return_value = {
            "meta_prompt": "Test prompt",
            "ai_response": "Test AI response",
            "generated_data": {"unknown_table": [{"id": 1}]}
        }

        generation_request = {
            "record_counts": {"unknown_table": 10, "another_unknown": 5}
        }

        response = client.post(
            f"/api/v1/generation/workflows/{sample_workflow_with_tables.id}/generate",
            json=generation_request
        )

        # Should still succeed - the service might handle unknown tables gracefully
        assert response.status_code == 200
        data = response.json()
        assert "task_id" in data

    def test_generation_task_response_structure(self, client: TestClient, sample_generation_task: GenerationTask):
        """Test generation task response has correct structure"""
        response = client.get(f"/api/v1/generation/tasks/{sample_generation_task.id}")

        assert response.status_code == 200
        data = response.json()

        # Check required fields
        required_fields = ["id", "workflow_id", "status", "created_at"]
        for field in required_fields:
            assert field in data

        # Check optional fields exist (even if None)
        optional_fields = ["record_counts", "generated_data", "error_message",
                          "meta_prompt", "ai_response", "started_at", "completed_at"]
        for field in optional_fields:
            assert field in data

        # Check field types
        assert isinstance(data["id"], int)
        assert isinstance(data["workflow_id"], int)
        assert isinstance(data["status"], str)
        assert isinstance(data["created_at"], str)

    def test_generation_task_timestamps(self, client: TestClient, db_session: Session, sample_workflow: Workflow):
        """Test generation task timestamps are properly set"""
        from datetime import datetime
        from sqlalchemy.sql import func

        # Create a completed task with timestamps
        now = func.now()
        task = GenerationTask(
            workflow_id=sample_workflow.id,
            status=GenerationStatus.COMPLETED,
            record_counts={"users": 5},
            started_at=now,
            completed_at=now
        )
        db_session.add(task)
        db_session.commit()
        db_session.refresh(task)

        response = client.get(f"/api/v1/generation/tasks/{task.id}")

        assert response.status_code == 200
        data = response.json()

        # Check timestamps are present and properly formatted
        assert data["created_at"] is not None
        assert data["started_at"] is not None
        assert data["completed_at"] is not None

        # Timestamps should be ISO format strings
        from datetime import datetime
        datetime.fromisoformat(data["created_at"].replace('Z', '+00:00'))

    @patch('app.services.deepseek_service.DeepSeekService.generate_data')
    def test_multiple_concurrent_generations(self, mock_generate_data, client: TestClient, sample_workflow_with_tables: Workflow):
        """Test multiple generation requests for the same workflow"""
        mock_generate_data.return_value = {
            "meta_prompt": "Test prompt",
            "ai_response": "Test AI response",
            "generated_data": {"users": [{"id": 1, "username": "test_user"}]}
        }

        generation_request = {
            "record_counts": {"users": 5}
        }

        # Start multiple generation tasks
        responses = []
        for _ in range(3):
            response = client.post(
                f"/api/v1/generation/workflows/{sample_workflow_with_tables.id}/generate",
                json=generation_request
            )
            responses.append(response)

        # All should succeed
        for response in responses:
            assert response.status_code == 200
            data = response.json()
            assert "task_id" in data

        # Task IDs should be different
        task_ids = [response.json()["task_id"] for response in responses]
        assert len(set(task_ids)) == 3  # All unique
