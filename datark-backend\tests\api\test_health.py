"""
Tests for health check API endpoints
"""
import pytest
from fastapi.testclient import TestClient


class TestHealthEndpoints:
    """Test health check endpoints"""
    
    def test_health_check_endpoint(self, client: TestClient):
        """Test health check endpoint returns correct status"""
        response = client.get("/api/v1/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["message"] == "DatArk API v1 is running"
    
    def test_health_check_content_type(self, client: TestClient):
        """Test health check endpoint returns JSON content type"""
        response = client.get("/api/v1/health")
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"
    
    def test_health_check_response_structure(self, client: TestClient):
        """Test health check endpoint response has correct structure"""
        response = client.get("/api/v1/health")
        
        assert response.status_code == 200
        data = response.json()
        
        # Check required fields
        assert "status" in data
        assert "message" in data
        
        # Check field types
        assert isinstance(data["status"], str)
        assert isinstance(data["message"], str)
    
    def test_health_check_multiple_requests(self, client: TestClient):
        """Test health check endpoint is consistent across multiple requests"""
        for _ in range(3):
            response = client.get("/api/v1/health")
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            assert data["message"] == "DatArk API v1 is running"
