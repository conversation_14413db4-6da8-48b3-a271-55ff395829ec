"""
Tests for workflow API endpoints
"""
import pytest
from fastapi.testclient import <PERSON><PERSON>lient
from sqlalchemy.orm import Session

from app.models.workflow import Workflow, WorkflowStatus


class TestWorkflowEndpoints:
    """Test workflow CRUD endpoints"""
    
    def test_create_workflow_success(self, client: TestClient):
        """Test successful workflow creation"""
        workflow_data = {
            "name": "Test Workflow",
            "description": "A test workflow"
        }
        
        response = client.post("/api/v1/workflows/", json=workflow_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == workflow_data["name"]
        assert data["description"] == workflow_data["description"]
        assert data["status"] == WorkflowStatus.DRAFT.value
        assert "id" in data
        assert "created_at" in data
    
    def test_create_workflow_minimal_data(self, client: TestClient):
        """Test workflow creation with minimal required data"""
        workflow_data = {
            "name": "Minimal Workflow"
        }
        
        response = client.post("/api/v1/workflows/", json=workflow_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == workflow_data["name"]
        assert data["description"] is None
        assert data["status"] == WorkflowStatus.DRAFT.value
    
    def test_create_workflow_invalid_data(self, client: TestClient):
        """Test workflow creation with invalid data"""
        # Empty name
        response = client.post("/api/v1/workflows/", json={"name": ""})
        assert response.status_code == 422
        
        # Missing name
        response = client.post("/api/v1/workflows/", json={"description": "No name"})
        assert response.status_code == 422
        
        # Name too long
        long_name = "x" * 256
        response = client.post("/api/v1/workflows/", json={"name": long_name})
        assert response.status_code == 422
    
    def test_get_workflows_empty_list(self, client: TestClient):
        """Test getting workflows when none exist"""
        response = client.get("/api/v1/workflows/")
        
        assert response.status_code == 200
        data = response.json()
        assert data["workflows"] == []
        assert data["total"] == 0
    
    def test_get_workflows_with_data(self, client: TestClient, sample_workflow: Workflow):
        """Test getting workflows when data exists"""
        response = client.get("/api/v1/workflows/")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["workflows"]) == 1
        assert data["total"] == 1
        assert data["workflows"][0]["id"] == sample_workflow.id
        assert data["workflows"][0]["name"] == sample_workflow.name
    
    def test_get_workflows_pagination(self, client: TestClient, db_session: Session):
        """Test workflow pagination"""
        # Create multiple workflows
        for i in range(5):
            workflow = Workflow(name=f"Workflow {i}", description=f"Description {i}")
            db_session.add(workflow)
        db_session.commit()
        
        # Test with limit
        response = client.get("/api/v1/workflows/?limit=3")
        assert response.status_code == 200
        data = response.json()
        assert len(data["workflows"]) == 3
        assert data["total"] == 5
        
        # Test with skip and limit
        response = client.get("/api/v1/workflows/?skip=2&limit=2")
        assert response.status_code == 200
        data = response.json()
        assert len(data["workflows"]) == 2
        assert data["total"] == 5
    
    def test_get_workflow_by_id_success(self, client: TestClient, sample_workflow: Workflow):
        """Test getting workflow by ID successfully"""
        response = client.get(f"/api/v1/workflows/{sample_workflow.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == sample_workflow.id
        assert data["name"] == sample_workflow.name
        assert data["description"] == sample_workflow.description
        assert data["status"] == sample_workflow.status.value
    
    def test_get_workflow_by_id_not_found(self, client: TestClient):
        """Test getting workflow by non-existent ID"""
        response = client.get("/api/v1/workflows/999")
        
        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "Workflow not found"
    
    def test_get_workflow_by_id_invalid_id(self, client: TestClient):
        """Test getting workflow with invalid ID format"""
        response = client.get("/api/v1/workflows/invalid")
        
        assert response.status_code == 422
    
    def test_update_workflow_success(self, client: TestClient, sample_workflow: Workflow):
        """Test successful workflow update"""
        update_data = {
            "name": "Updated Workflow",
            "description": "Updated description",
            "status": WorkflowStatus.COMPLETED.value
        }
        
        response = client.put(f"/api/v1/workflows/{sample_workflow.id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["description"] == update_data["description"]
        assert data["status"] == update_data["status"]
        assert data["id"] == sample_workflow.id
    
    def test_update_workflow_partial(self, client: TestClient, sample_workflow: Workflow):
        """Test partial workflow update"""
        update_data = {"name": "Partially Updated"}
        
        response = client.put(f"/api/v1/workflows/{sample_workflow.id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["description"] == sample_workflow.description  # Unchanged
        assert data["status"] == sample_workflow.status.value  # Unchanged
    
    def test_update_workflow_not_found(self, client: TestClient):
        """Test updating non-existent workflow"""
        update_data = {"name": "Non-existent"}
        
        response = client.put("/api/v1/workflows/999", json=update_data)
        
        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "Workflow not found"
    
    def test_update_workflow_invalid_data(self, client: TestClient, sample_workflow: Workflow):
        """Test updating workflow with invalid data"""
        # Empty name
        response = client.put(f"/api/v1/workflows/{sample_workflow.id}", json={"name": ""})
        assert response.status_code == 422
        
        # Name too long
        long_name = "x" * 256
        response = client.put(f"/api/v1/workflows/{sample_workflow.id}", json={"name": long_name})
        assert response.status_code == 422
    
    def test_delete_workflow_success(self, client: TestClient, sample_workflow: Workflow):
        """Test successful workflow deletion"""
        response = client.delete(f"/api/v1/workflows/{sample_workflow.id}")
        
        assert response.status_code == 204
        
        # Verify workflow is deleted
        get_response = client.get(f"/api/v1/workflows/{sample_workflow.id}")
        assert get_response.status_code == 404
    
    def test_delete_workflow_not_found(self, client: TestClient):
        """Test deleting non-existent workflow"""
        response = client.delete("/api/v1/workflows/999")
        
        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "Workflow not found"
    
    def test_delete_workflow_invalid_id(self, client: TestClient):
        """Test deleting workflow with invalid ID format"""
        response = client.delete("/api/v1/workflows/invalid")
        
        assert response.status_code == 422
