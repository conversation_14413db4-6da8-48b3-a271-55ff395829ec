"""
Test configuration and fixtures
"""
import pytest
import asyncio
from typing import Generator, AsyncGenerator
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from app.core.database import Base, get_db
from app.core.config import Settings
from main import app
from app.models.workflow import Workflow, DataTable, WorkflowStatus
from app.models.table_field import TableField, FieldType
from app.models.relationship import TableRelationship, RelationshipType
from app.models.generation import GenerationTask, GenerationStatus


# Test database URL (SQLite in memory)
TEST_DATABASE_URL = "sqlite:///:memory:"

# Create test engine
test_engine = create_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

# Create test session factory
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session() -> Generator[Session, None, None]:
    """Create a fresh database session for each test."""
    # Create all tables
    Base.metadata.create_all(bind=test_engine)
    
    # Create session
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        # Drop all tables after test
        Base.metadata.drop_all(bind=test_engine)


@pytest.fixture(scope="function")
def test_settings():
    """Create test settings."""
    return Settings(
        API_V1_STR="/api/v1",
        SECRET_KEY="test-secret-key",
        DEBUG=True,
        DATABASE_URL=TEST_DATABASE_URL,
        DEEPSEEK_API_KEY="test-api-key",
        DEEPSEEK_API_URL="https://api.deepseek.com/v1"
    )


@pytest.fixture(scope="function")
def client(db_session: Session) -> Generator[TestClient, None, None]:
    """Create a test client with database dependency override."""
    
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


# Test data fixtures
@pytest.fixture
def sample_workflow(db_session: Session) -> Workflow:
    """Create a sample workflow for testing."""
    workflow = Workflow(
        name="Test Workflow",
        description="A test workflow for unit testing",
        status=WorkflowStatus.DRAFT
    )
    db_session.add(workflow)
    db_session.commit()
    db_session.refresh(workflow)
    return workflow


@pytest.fixture
def sample_table(db_session: Session, sample_workflow: Workflow) -> DataTable:
    """Create a sample data table for testing."""
    table = DataTable(
        name="users",
        alias="用户表",
        workflow_id=sample_workflow.id,
        position_x=100,
        position_y=200
    )
    db_session.add(table)
    db_session.commit()
    db_session.refresh(table)
    return table


@pytest.fixture
def sample_field(db_session: Session, sample_table: DataTable) -> TableField:
    """Create a sample table field for testing."""
    field = TableField(
        name="username",
        field_type=FieldType.TEXT,
        ai_prompt="Generate a unique username",
        is_required=True,
        is_unique=True,
        table_id=sample_table.id,
        order_index=1
    )
    db_session.add(field)
    db_session.commit()
    db_session.refresh(field)
    return field


@pytest.fixture
def sample_workflow_with_tables(db_session: Session) -> Workflow:
    """Create a workflow with multiple tables and fields for complex testing."""
    # Create workflow
    workflow = Workflow(
        name="Complex Test Workflow",
        description="A workflow with multiple tables and relationships",
        status=WorkflowStatus.DRAFT
    )
    db_session.add(workflow)
    db_session.commit()
    db_session.refresh(workflow)
    
    # Create users table
    users_table = DataTable(
        name="users",
        alias="用户表",
        workflow_id=workflow.id,
        position_x=100,
        position_y=100
    )
    db_session.add(users_table)
    
    # Create posts table
    posts_table = DataTable(
        name="posts",
        alias="文章表",
        workflow_id=workflow.id,
        position_x=300,
        position_y=100
    )
    db_session.add(posts_table)
    db_session.commit()
    db_session.refresh(users_table)
    db_session.refresh(posts_table)
    
    # Add fields to users table
    user_fields = [
        TableField(name="id", field_type=FieldType.NUMBER, is_primary_key=True, table_id=users_table.id, order_index=1),
        TableField(name="username", field_type=FieldType.TEXT, is_unique=True, table_id=users_table.id, order_index=2),
        TableField(name="email", field_type=FieldType.EMAIL, is_unique=True, table_id=users_table.id, order_index=3),
    ]
    
    # Add fields to posts table
    post_fields = [
        TableField(name="id", field_type=FieldType.NUMBER, is_primary_key=True, table_id=posts_table.id, order_index=1),
        TableField(name="title", field_type=FieldType.TEXT, is_required=True, table_id=posts_table.id, order_index=2),
        TableField(name="content", field_type=FieldType.TEXT, table_id=posts_table.id, order_index=3),
        TableField(name="user_id", field_type=FieldType.NUMBER, is_required=True, table_id=posts_table.id, order_index=4),
    ]
    
    for field in user_fields + post_fields:
        db_session.add(field)
    
    # Create relationship
    relationship = TableRelationship(
        relationship_type=RelationshipType.ONE_TO_MANY,
        description="One user can have many posts",
        source_table_id=users_table.id,
        target_table_id=posts_table.id,
        workflow_id=workflow.id
    )
    db_session.add(relationship)
    
    db_session.commit()
    db_session.refresh(workflow)
    return workflow


@pytest.fixture
def sample_generation_task(db_session: Session, sample_workflow: Workflow) -> GenerationTask:
    """Create a sample generation task for testing."""
    task = GenerationTask(
        workflow_id=sample_workflow.id,
        status=GenerationStatus.PENDING,
        record_counts={"users": 10, "posts": 50}
    )
    db_session.add(task)
    db_session.commit()
    db_session.refresh(task)
    return task
