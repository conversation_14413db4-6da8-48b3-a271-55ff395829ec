"""
Tests for main application endpoints
"""
import pytest
from fastapi.testclient import TestClient


class TestMainEndpoints:
    """Test main application endpoints"""
    
    def test_root_endpoint(self, client: TestClient):
        """Test root endpoint returns correct message"""
        response = client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        assert data == {"message": "DatArk API is running"}
    
    def test_root_endpoint_content_type(self, client: TestClient):
        """Test root endpoint returns JSON content type"""
        response = client.get("/")
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"
    
    def test_openapi_docs_accessible(self, client: TestClient):
        """Test that OpenAPI docs are accessible"""
        response = client.get("/api/v1/openapi.json")
        
        assert response.status_code == 200
        data = response.json()
        assert "openapi" in data
        assert data["info"]["title"] == "DataRK API"
        assert data["info"]["version"] == "1.0.0"
    
    def test_docs_ui_accessible(self, client: TestClient):
        """Test that Swagger UI docs are accessible"""
        response = client.get("/docs")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
    
    def test_redoc_ui_accessible(self, client: TestClient):
        """Test that ReDoc UI is accessible"""
        response = client.get("/redoc")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
