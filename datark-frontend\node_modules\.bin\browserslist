#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Workspace/augment/datark/datark-frontend/node_modules/.pnpm/browserslist@4.25.1/node_modules/browserslist/node_modules:/mnt/c/Users/<USER>/Workspace/augment/datark/datark-frontend/node_modules/.pnpm/browserslist@4.25.1/node_modules:/mnt/c/Users/<USER>/Workspace/augment/datark/datark-frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Workspace/augment/datark/datark-frontend/node_modules/.pnpm/browserslist@4.25.1/node_modules/browserslist/node_modules:/mnt/c/Users/<USER>/Workspace/augment/datark/datark-frontend/node_modules/.pnpm/browserslist@4.25.1/node_modules:/mnt/c/Users/<USER>/Workspace/augment/datark/datark-frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/browserslist@4.25.1/node_modules/browserslist/cli.js" "$@"
else
  exec node  "$basedir/../.pnpm/browserslist@4.25.1/node_modules/browserslist/cli.js" "$@"
fi
