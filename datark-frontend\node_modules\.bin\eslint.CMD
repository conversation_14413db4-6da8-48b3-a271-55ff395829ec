@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Workspace\augment\datark\datark-frontend\node_modules\.pnpm\eslint@9.32.0_jiti@2.5.1\node_modules\eslint\bin\node_modules;C:\Users\<USER>\Workspace\augment\datark\datark-frontend\node_modules\.pnpm\eslint@9.32.0_jiti@2.5.1\node_modules\eslint\node_modules;C:\Users\<USER>\Workspace\augment\datark\datark-frontend\node_modules\.pnpm\eslint@9.32.0_jiti@2.5.1\node_modules;C:\Users\<USER>\Workspace\augment\datark\datark-frontend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Workspace\augment\datark\datark-frontend\node_modules\.pnpm\eslint@9.32.0_jiti@2.5.1\node_modules\eslint\bin\node_modules;C:\Users\<USER>\Workspace\augment\datark\datark-frontend\node_modules\.pnpm\eslint@9.32.0_jiti@2.5.1\node_modules\eslint\node_modules;C:\Users\<USER>\Workspace\augment\datark\datark-frontend\node_modules\.pnpm\eslint@9.32.0_jiti@2.5.1\node_modules;C:\Users\<USER>\Workspace\augment\datark\datark-frontend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\eslint@9.32.0_jiti@2.5.1\node_modules\eslint\bin\eslint.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\eslint@9.32.0_jiti@2.5.1\node_modules\eslint\bin\eslint.js" %*
)
