{"hash": "4f9a0cae", "configHash": "8fca6597", "lockfileHash": "c239cea0", "browserHash": "2e3cdd12", "optimized": {"react": {"src": "../../.pnpm/react@19.1.0/node_modules/react/index.js", "file": "react.js", "fileHash": "37b26e43", "needsInterop": true}, "react-dom": {"src": "../../.pnpm/react-dom@19.1.0_react@19.1.0/node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "dff908a7", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../.pnpm/react@19.1.0/node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "9f3b6206", "needsInterop": true}, "react/jsx-runtime": {"src": "../../.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "4d6b4aae", "needsInterop": true}, "@radix-ui/react-dialog": {"src": "../../.pnpm/@radix-ui+react-dialog@1.1._ebf14a846abc2fe74b19ca0ca406c133/node_modules/@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "dbd52b67", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../.pnpm/@radix-ui+react-label@2.1.7_f026c130782473ba8001b4f96e481e94/node_modules/@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "1b8ef2a7", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../.pnpm/@radix-ui+react-progress@1._81300e550e89fc43ba6c1113605c4967/node_modules/@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "39782438", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../.pnpm/@radix-ui+react-scroll-area_6b0f79a3571a51da2042bcade1180496/node_modules/@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "1149d689", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "2d15c8a9", "needsInterop": false}, "axios": {"src": "../../.pnpm/axios@1.11.0/node_modules/axios/index.js", "file": "axios.js", "fileHash": "70243f04", "needsInterop": false}, "class-variance-authority": {"src": "../../.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "41ba3009", "needsInterop": false}, "clsx": {"src": "../../.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "9c56da71", "needsInterop": false}, "lucide-react": {"src": "../../.pnpm/lucide-react@0.526.0_react@19.1.0/node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "c0ec2c8c", "needsInterop": false}, "react-dom/client": {"src": "../../.pnpm/react-dom@19.1.0_react@19.1.0/node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "a38cd1ba", "needsInterop": true}, "reactflow": {"src": "../../.pnpm/reactflow@11.11.4_@types+re_e817128831c8754898fd2dd97b67e48d/node_modules/reactflow/dist/esm/index.mjs", "file": "reactflow.js", "fileHash": "8283d1b1", "needsInterop": false}, "sonner": {"src": "../../.pnpm/sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "3dc84205", "needsInterop": false}, "tailwind-merge": {"src": "../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "4c5e8aa6", "needsInterop": false}}, "chunks": {"chunk-DEX2RCYB": {"file": "chunk-DEX2RCYB.js"}, "chunk-ZIV7Q47A": {"file": "chunk-ZIV7Q47A.js"}, "chunk-T6QXNCIG": {"file": "chunk-T6QXNCIG.js"}, "chunk-UUOMBTGY": {"file": "chunk-UUOMBTGY.js"}, "chunk-5MTWFNAB": {"file": "chunk-5MTWFNAB.js"}, "chunk-LIEQ75ZC": {"file": "chunk-LIEQ75ZC.js"}, "chunk-OBBQL43Q": {"file": "chunk-OBBQL43Q.js"}, "chunk-PLUGHXRK": {"file": "chunk-PLUGHXRK.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}