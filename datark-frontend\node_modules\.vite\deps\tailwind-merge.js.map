{"version": 3, "sources": ["../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/src/lib/class-group-utils.ts", "../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/src/lib/lru-cache.ts", "../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/src/lib/parse-class-name.ts", "../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/src/lib/sort-modifiers.ts", "../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/src/lib/config-utils.ts", "../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/src/lib/merge-classlist.ts", "../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/src/lib/tw-join.ts", "../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/src/lib/create-tailwind-merge.ts", "../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/src/lib/from-theme.ts", "../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/src/lib/validators.ts", "../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/src/lib/default-config.ts", "../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/src/lib/merge-configs.ts", "../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/src/lib/extend-tailwind-merge.ts", "../../.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/src/lib/tw-merge.ts"], "sourcesContent": ["import {\n    AnyClassGroupIds,\n    AnyConfig,\n    AnyThemeGroupIds,\n    ClassGroup,\n    ClassValidator,\n    Config,\n    ThemeGetter,\n    ThemeObject,\n} from './types'\n\nexport interface ClassPartObject {\n    nextPart: Map<string, ClassPartObject>\n    validators: ClassValidatorObject[]\n    classGroupId?: AnyClassGroupIds\n}\n\ninterface ClassValidatorObject {\n    classGroupId: AnyClassGroupIds\n    validator: ClassValidator\n}\n\nconst CLASS_PART_SEPARATOR = '-'\n\nexport const createClassGroupUtils = (config: AnyConfig) => {\n    const classMap = createClassMap(config)\n    const { conflictingClassGroups, conflictingClassGroupModifiers } = config\n\n    const getClassGroupId = (className: string) => {\n        const classParts = className.split(CLASS_PART_SEPARATOR)\n\n        // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n        if (classParts[0] === '' && classParts.length !== 1) {\n            classParts.shift()\n        }\n\n        return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className)\n    }\n\n    const getConflictingClassGroupIds = (\n        classGroupId: AnyClassGroupIds,\n        hasPostfixModifier: boolean,\n    ) => {\n        const conflicts = conflictingClassGroups[classGroupId] || []\n\n        if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n            return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]!]\n        }\n\n        return conflicts\n    }\n\n    return {\n        getClassGroupId,\n        getConflictingClassGroupIds,\n    }\n}\n\nconst getGroupRecursive = (\n    classParts: string[],\n    classPartObject: ClassPartObject,\n): AnyClassGroupIds | undefined => {\n    if (classParts.length === 0) {\n        return classPartObject.classGroupId\n    }\n\n    const currentClassPart = classParts[0]!\n    const nextClassPartObject = classPartObject.nextPart.get(currentClassPart)\n    const classGroupFromNextClassPart = nextClassPartObject\n        ? getGroupRecursive(classParts.slice(1), nextClassPartObject)\n        : undefined\n\n    if (classGroupFromNextClassPart) {\n        return classGroupFromNextClassPart\n    }\n\n    if (classPartObject.validators.length === 0) {\n        return undefined\n    }\n\n    const classRest = classParts.join(CLASS_PART_SEPARATOR)\n\n    return classPartObject.validators.find(({ validator }) => validator(classRest))?.classGroupId\n}\n\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/\n\nconst getGroupIdForArbitraryProperty = (className: string) => {\n    if (arbitraryPropertyRegex.test(className)) {\n        const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)![1]\n        const property = arbitraryPropertyClassName?.substring(\n            0,\n            arbitraryPropertyClassName.indexOf(':'),\n        )\n\n        if (property) {\n            // I use two dots here because one dot is used as prefix for class groups in plugins\n            return 'arbitrary..' + property\n        }\n    }\n}\n\n/**\n * Exported for testing only\n */\nexport const createClassMap = (config: Config<AnyClassGroupIds, AnyThemeGroupIds>) => {\n    const { theme, classGroups } = config\n    const classMap: ClassPartObject = {\n        nextPart: new Map<string, ClassPartObject>(),\n        validators: [],\n    }\n\n    for (const classGroupId in classGroups) {\n        processClassesRecursively(classGroups[classGroupId]!, classMap, classGroupId, theme)\n    }\n\n    return classMap\n}\n\nconst processClassesRecursively = (\n    classGroup: ClassGroup<AnyThemeGroupIds>,\n    classPartObject: ClassPartObject,\n    classGroupId: AnyClassGroupIds,\n    theme: ThemeObject<AnyThemeGroupIds>,\n) => {\n    classGroup.forEach((classDefinition) => {\n        if (typeof classDefinition === 'string') {\n            const classPartObjectToEdit =\n                classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition)\n            classPartObjectToEdit.classGroupId = classGroupId\n            return\n        }\n\n        if (typeof classDefinition === 'function') {\n            if (isThemeGetter(classDefinition)) {\n                processClassesRecursively(\n                    classDefinition(theme),\n                    classPartObject,\n                    classGroupId,\n                    theme,\n                )\n                return\n            }\n\n            classPartObject.validators.push({\n                validator: classDefinition,\n                classGroupId,\n            })\n\n            return\n        }\n\n        Object.entries(classDefinition).forEach(([key, classGroup]) => {\n            processClassesRecursively(\n                classGroup,\n                getPart(classPartObject, key),\n                classGroupId,\n                theme,\n            )\n        })\n    })\n}\n\nconst getPart = (classPartObject: ClassPartObject, path: string) => {\n    let currentClassPartObject = classPartObject\n\n    path.split(CLASS_PART_SEPARATOR).forEach((pathPart) => {\n        if (!currentClassPartObject.nextPart.has(pathPart)) {\n            currentClassPartObject.nextPart.set(pathPart, {\n                nextPart: new Map(),\n                validators: [],\n            })\n        }\n\n        currentClassPartObject = currentClassPartObject.nextPart.get(pathPart)!\n    })\n\n    return currentClassPartObject\n}\n\nconst isThemeGetter = (func: ClassValidator | ThemeGetter): func is ThemeGetter =>\n    (func as ThemeGetter).isThemeGetter\n", "// Export is needed because TypeScript complains about an error otherwise:\n// Error: …/tailwind-merge/src/config-utils.ts(8,17): semantic error TS4058: Return type of exported function has or is using name 'LruCache' from external module \"…/tailwind-merge/src/lru-cache\" but cannot be named.\nexport interface LruCache<Key, Value> {\n    get(key: Key): Value | undefined\n    set(key: Key, value: Value): void\n}\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nexport const createLruCache = <Key, Value>(maxCacheSize: number): LruCache<Key, Value> => {\n    if (maxCacheSize < 1) {\n        return {\n            get: () => undefined,\n            set: () => {},\n        }\n    }\n\n    let cacheSize = 0\n    let cache = new Map<Key, Value>()\n    let previousCache = new Map<Key, Value>()\n\n    const update = (key: Key, value: Value) => {\n        cache.set(key, value)\n        cacheSize++\n\n        if (cacheSize > maxCacheSize) {\n            cacheSize = 0\n            previousCache = cache\n            cache = new Map()\n        }\n    }\n\n    return {\n        get(key) {\n            let value = cache.get(key)\n\n            if (value !== undefined) {\n                return value\n            }\n            if ((value = previousCache.get(key)) !== undefined) {\n                update(key, value)\n                return value\n            }\n        },\n        set(key, value) {\n            if (cache.has(key)) {\n                cache.set(key, value)\n            } else {\n                update(key, value)\n            }\n        },\n    }\n}\n", "import { AnyConfig, ParsedClassName } from './types'\n\nexport const IMPORTANT_MODIFIER = '!'\nconst MODIFIER_SEPARATOR = ':'\nconst MODIFIER_SEPARATOR_LENGTH = MODIFIER_SEPARATOR.length\n\nexport const createParseClassName = (config: AnyConfig) => {\n    const { prefix, experimentalParseClassName } = config\n\n    /**\n     * Parse class name into parts.\n     *\n     * Inspired by `splitAtTopLevelOnly` used in Tailwind CSS\n     * @see https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n     */\n    let parseClassName = (className: string): ParsedClassName => {\n        const modifiers = []\n\n        let bracketDepth = 0\n        let parenDepth = 0\n        let modifierStart = 0\n        let postfixModifierPosition: number | undefined\n\n        for (let index = 0; index < className.length; index++) {\n            let currentCharacter = className[index]\n\n            if (bracketDepth === 0 && parenDepth === 0) {\n                if (currentCharacter === MODIFIER_SEPARATOR) {\n                    modifiers.push(className.slice(modifierStart, index))\n                    modifierStart = index + MODIFIER_SEPARATOR_LENGTH\n                    continue\n                }\n\n                if (currentCharacter === '/') {\n                    postfixModifierPosition = index\n                    continue\n                }\n            }\n\n            if (currentCharacter === '[') {\n                bracketDepth++\n            } else if (currentCharacter === ']') {\n                bracketDepth--\n            } else if (currentCharacter === '(') {\n                parenDepth++\n            } else if (currentCharacter === ')') {\n                parenDepth--\n            }\n        }\n\n        const baseClassNameWithImportantModifier =\n            modifiers.length === 0 ? className : className.substring(modifierStart)\n        const baseClassName = stripImportantModifier(baseClassNameWithImportantModifier)\n        const hasImportantModifier = baseClassName !== baseClassNameWithImportantModifier\n        const maybePostfixModifierPosition =\n            postfixModifierPosition && postfixModifierPosition > modifierStart\n                ? postfixModifierPosition - modifierStart\n                : undefined\n\n        return {\n            modifiers,\n            hasImportantModifier,\n            baseClassName,\n            maybePostfixModifierPosition,\n        }\n    }\n\n    if (prefix) {\n        const fullPrefix = prefix + MODIFIER_SEPARATOR\n        const parseClassNameOriginal = parseClassName\n        parseClassName = (className) =>\n            className.startsWith(fullPrefix)\n                ? parseClassNameOriginal(className.substring(fullPrefix.length))\n                : {\n                      isExternal: true,\n                      modifiers: [],\n                      hasImportantModifier: false,\n                      baseClassName: className,\n                      maybePostfixModifierPosition: undefined,\n                  }\n    }\n\n    if (experimentalParseClassName) {\n        const parseClassNameOriginal = parseClassName\n        parseClassName = (className) =>\n            experimentalParseClassName({ className, parseClassName: parseClassNameOriginal })\n    }\n\n    return parseClassName\n}\n\nconst stripImportantModifier = (baseClassName: string) => {\n    if (baseClassName.endsWith(IMPORTANT_MODIFIER)) {\n        return baseClassName.substring(0, baseClassName.length - 1)\n    }\n\n    /**\n     * In Tailwind CSS v3 the important modifier was at the start of the base class name. This is still supported for legacy reasons.\n     * @see https://github.com/dcastil/tailwind-merge/issues/513#issuecomment-2614029864\n     */\n    if (baseClassName.startsWith(IMPORTANT_MODIFIER)) {\n        return baseClassName.substring(1)\n    }\n\n    return baseClassName\n}\n", "import { AnyConfig } from './types'\n\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nexport const createSortModifiers = (config: AnyConfig) => {\n    const orderSensitiveModifiers = Object.fromEntries(\n        config.orderSensitiveModifiers.map((modifier) => [modifier, true]),\n    )\n\n    const sortModifiers = (modifiers: string[]) => {\n        if (modifiers.length <= 1) {\n            return modifiers\n        }\n\n        const sortedModifiers: string[] = []\n        let unsortedModifiers: string[] = []\n\n        modifiers.forEach((modifier) => {\n            const isPositionSensitive = modifier[0] === '[' || orderSensitiveModifiers[modifier]\n\n            if (isPositionSensitive) {\n                sortedModifiers.push(...unsortedModifiers.sort(), modifier)\n                unsortedModifiers = []\n            } else {\n                unsortedModifiers.push(modifier)\n            }\n        })\n\n        sortedModifiers.push(...unsortedModifiers.sort())\n\n        return sortedModifiers\n    }\n\n    return sortModifiers\n}\n", "import { createClassGroupUtils } from './class-group-utils'\nimport { createLruCache } from './lru-cache'\nimport { createParseClassName } from './parse-class-name'\nimport { createSortModifiers } from './sort-modifiers'\nimport { AnyConfig } from './types'\n\nexport type ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport const createConfigUtils = (config: AnyConfig) => ({\n    cache: createLruCache<string, string>(config.cacheSize),\n    parseClassName: createParseClassName(config),\n    sortModifiers: createSortModifiers(config),\n    ...createClassGroupUtils(config),\n})\n", "import { ConfigUtils } from './config-utils'\nimport { IMPORTANT_MODIFIER } from './parse-class-name'\n\nconst SPLIT_CLASSES_REGEX = /\\s+/\n\nexport const mergeClassList = (classList: string, configUtils: ConfigUtils) => {\n    const { parseClassName, getClassGroupId, getConflictingClassGroupIds, sortModifiers } =\n        configUtils\n\n    /**\n     * Set of classGroupIds in following format:\n     * `{importantModifier}{variantModifiers}{classGroupId}`\n     * @example 'float'\n     * @example 'hover:focus:bg-color'\n     * @example 'md:!pr'\n     */\n    const classGroupsInConflict: string[] = []\n    const classNames = classList.trim().split(SPLIT_CLASSES_REGEX)\n\n    let result = ''\n\n    for (let index = classNames.length - 1; index >= 0; index -= 1) {\n        const originalClassName = classNames[index]!\n\n        const {\n            isExternal,\n            modifiers,\n            hasImportantModifier,\n            baseClassName,\n            maybePostfixModifierPosition,\n        } = parseClassName(originalClassName)\n\n        if (isExternal) {\n            result = originalClassName + (result.length > 0 ? ' ' + result : result)\n            continue\n        }\n\n        let hasPostfixModifier = !!maybePostfixModifierPosition\n        let classGroupId = getClassGroupId(\n            hasPostfixModifier\n                ? baseClassName.substring(0, maybePostfixModifierPosition)\n                : baseClassName,\n        )\n\n        if (!classGroupId) {\n            if (!hasPostfixModifier) {\n                // Not a Tailwind class\n                result = originalClassName + (result.length > 0 ? ' ' + result : result)\n                continue\n            }\n\n            classGroupId = getClassGroupId(baseClassName)\n\n            if (!classGroupId) {\n                // Not a Tailwind class\n                result = originalClassName + (result.length > 0 ? ' ' + result : result)\n                continue\n            }\n\n            hasPostfixModifier = false\n        }\n\n        const variantModifier = sortModifiers(modifiers).join(':')\n\n        const modifierId = hasImportantModifier\n            ? variantModifier + IMPORTANT_MODIFIER\n            : variantModifier\n\n        const classId = modifierId + classGroupId\n\n        if (classGroupsInConflict.includes(classId)) {\n            // Tailwind class omitted due to conflict\n            continue\n        }\n\n        classGroupsInConflict.push(classId)\n\n        const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier)\n        for (let i = 0; i < conflictGroups.length; ++i) {\n            const group = conflictGroups[i]!\n            classGroupsInConflict.push(modifierId + group)\n        }\n\n        // Tailwind class not in conflict\n        result = originalClassName + (result.length > 0 ? ' ' + result : result)\n    }\n\n    return result\n}\n", "/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) <PERSON> <<EMAIL>> (lukeed.com)\n */\n\nexport type ClassNameValue = ClassNameArray | string | null | undefined | 0 | 0n | false\ntype ClassNameArray = ClassNameValue[]\n\nexport function twJoin(...classLists: ClassNameValue[]): string\nexport function twJoin() {\n    let index = 0\n    let argument: ClassNameValue\n    let resolvedValue: string\n    let string = ''\n\n    while (index < arguments.length) {\n        if ((argument = arguments[index++])) {\n            if ((resolvedValue = toValue(argument))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n    return string\n}\n\nconst toValue = (mix: ClassNameArray | string) => {\n    if (typeof mix === 'string') {\n        return mix\n    }\n\n    let resolvedValue: string\n    let string = ''\n\n    for (let k = 0; k < mix.length; k++) {\n        if (mix[k]) {\n            if ((resolvedValue = toValue(mix[k] as ClassNameArray | string))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n\n    return string\n}\n", "import { createConfigUtils } from './config-utils'\nimport { mergeClassList } from './merge-classlist'\nimport { ClassNameValue, twJoin } from './tw-join'\nimport { AnyConfig } from './types'\n\ntype CreateConfigFirst = () => AnyConfig\ntype CreateConfigSubsequent = (config: AnyConfig) => AnyConfig\ntype TailwindMerge = (...classLists: ClassNameValue[]) => string\ntype ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport function createTailwindMerge(\n    createConfigFirst: CreateConfigFirst,\n    ...createConfigRest: CreateConfigSubsequent[]\n): TailwindMerge {\n    let configUtils: ConfigUtils\n    let cacheGet: ConfigUtils['cache']['get']\n    let cacheSet: ConfigUtils['cache']['set']\n    let functionToCall = initTailwindMerge\n\n    function initTailwindMerge(classList: string) {\n        const config = createConfigRest.reduce(\n            (previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig),\n            createConfigFirst() as AnyConfig,\n        )\n\n        configUtils = createConfigUtils(config)\n        cacheGet = configUtils.cache.get\n        cacheSet = configUtils.cache.set\n        functionToCall = tailwindMerge\n\n        return tailwindMerge(classList)\n    }\n\n    function tailwindMerge(classList: string) {\n        const cachedResult = cacheGet(classList)\n\n        if (cachedResult) {\n            return cachedResult\n        }\n\n        const result = mergeClassList(classList, configUtils)\n        cacheSet(classList, result)\n\n        return result\n    }\n\n    return function callTailwindMerge() {\n        return functionToCall(twJoin.apply(null, arguments as any))\n    }\n}\n", "import { DefaultThemeGroupIds, <PERSON><PERSON><PERSON><PERSON>, ThemeGetter, ThemeObject } from './types'\n\nexport const fromTheme = <\n    AdditionalThemeGroupIds extends string = never,\n    DefaultThemeGroupIdsInner extends string = DefaultThemeGroupIds,\n>(key: NoInfer<DefaultThemeGroupIdsInner | AdditionalThemeGroupIds>): ThemeGetter => {\n    const themeGetter = (theme: ThemeObject<DefaultThemeGroupIdsInner | AdditionalThemeGroupIds>) =>\n        theme[key] || []\n\n    themeGetter.isThemeGetter = true as const\n\n    return themeGetter\n}\n", "const arbitraryValueRegex = /^\\[(?:(\\w[\\w-]*):)?(.+)\\]$/i\nconst arbitraryVariableRegex = /^\\((?:(\\w[\\w-]*):)?(.+)\\)$/i\nconst fractionRegex = /^\\d+\\/\\d+$/\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/\nconst lengthUnitRegex =\n    /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\\(.+\\)$/\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/\nconst imageRegex =\n    /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/\n\nexport const isFraction = (value: string) => fractionRegex.test(value)\n\nexport const isNumber = (value: string) => !!value && !Number.isNaN(Number(value))\n\nexport const isInteger = (value: string) => !!value && Number.isInteger(Number(value))\n\nexport const isPercent = (value: string) => value.endsWith('%') && isNumber(value.slice(0, -1))\n\nexport const isTshirtSize = (value: string) => tshirtUnitRegex.test(value)\n\nexport const isAny = () => true\n\nconst isLengthOnly = (value: string) =>\n    // `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n    // For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n    // I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\n    lengthUnitRegex.test(value) && !colorFunctionRegex.test(value)\n\nconst isNever = () => false\n\nconst isShadow = (value: string) => shadowRegex.test(value)\n\nconst isImage = (value: string) => imageRegex.test(value)\n\nexport const isAnyNonArbitrary = (value: string) =>\n    !isArbitraryValue(value) && !isArbitraryVariable(value)\n\nexport const isArbitrarySize = (value: string) => getIsArbitraryValue(value, isLabelSize, isNever)\n\nexport const isArbitraryValue = (value: string) => arbitraryValueRegex.test(value)\n\nexport const isArbitraryLength = (value: string) =>\n    getIsArbitraryValue(value, isLabelLength, isLengthOnly)\n\nexport const isArbitraryNumber = (value: string) =>\n    getIsArbitraryValue(value, isLabelNumber, isNumber)\n\nexport const isArbitraryPosition = (value: string) =>\n    getIsArbitraryValue(value, isLabelPosition, isNever)\n\nexport const isArbitraryImage = (value: string) => getIsArbitraryValue(value, isLabelImage, isImage)\n\nexport const isArbitraryShadow = (value: string) =>\n    getIsArbitraryValue(value, isLabelShadow, isShadow)\n\nexport const isArbitraryVariable = (value: string) => arbitraryVariableRegex.test(value)\n\nexport const isArbitraryVariableLength = (value: string) =>\n    getIsArbitraryVariable(value, isLabelLength)\n\nexport const isArbitraryVariableFamilyName = (value: string) =>\n    getIsArbitraryVariable(value, isLabelFamilyName)\n\nexport const isArbitraryVariablePosition = (value: string) =>\n    getIsArbitraryVariable(value, isLabelPosition)\n\nexport const isArbitraryVariableSize = (value: string) => getIsArbitraryVariable(value, isLabelSize)\n\nexport const isArbitraryVariableImage = (value: string) =>\n    getIsArbitraryVariable(value, isLabelImage)\n\nexport const isArbitraryVariableShadow = (value: string) =>\n    getIsArbitraryVariable(value, isLabelShadow, true)\n\n// Helpers\n\nconst getIsArbitraryValue = (\n    value: string,\n    testLabel: (label: string) => boolean,\n    testValue: (value: string) => boolean,\n) => {\n    const result = arbitraryValueRegex.exec(value)\n\n    if (result) {\n        if (result[1]) {\n            return testLabel(result[1])\n        }\n\n        return testValue(result[2]!)\n    }\n\n    return false\n}\n\nconst getIsArbitraryVariable = (\n    value: string,\n    testLabel: (label: string) => boolean,\n    shouldMatchNoLabel = false,\n) => {\n    const result = arbitraryVariableRegex.exec(value)\n\n    if (result) {\n        if (result[1]) {\n            return testLabel(result[1])\n        }\n        return shouldMatchNoLabel\n    }\n\n    return false\n}\n\n// Labels\n\nconst isLabelPosition = (label: string) => label === 'position' || label === 'percentage'\n\nconst isLabelImage = (label: string) => label === 'image' || label === 'url'\n\nconst isLabelSize = (label: string) => label === 'length' || label === 'size' || label === 'bg-size'\n\nconst isLabelLength = (label: string) => label === 'length'\n\nconst isLabelNumber = (label: string) => label === 'number'\n\nconst isLabelFamilyName = (label: string) => label === 'family-name'\n\nconst isLabelShadow = (label: string) => label === 'shadow'\n", "import { fromTheme } from './from-theme'\nimport { Config, DefaultClassGroupIds, DefaultThemeGroupIds } from './types'\nimport {\n    isAny,\n    isAnyNonArbitrary,\n    isArbitraryImage,\n    isArbitraryLength,\n    isArbitraryNumber,\n    isArbitraryPosition,\n    isArbitraryShadow,\n    isArbitrarySize,\n    isArbitraryValue,\n    isArbitraryVariable,\n    isArbitraryVariableFamilyName,\n    isArbitraryVariableImage,\n    isArbitraryVariableLength,\n    isArbitraryVariablePosition,\n    isArbitraryVariableShadow,\n    isArbitraryVariableSize,\n    isFraction,\n    isInteger,\n    isNumber,\n    isPercent,\n    isTshirtSize,\n} from './validators'\n\nexport const getDefaultConfig = () => {\n    /**\n     * Theme getters for theme variable namespaces\n     * @see https://tailwindcss.com/docs/theme#theme-variable-namespaces\n     */\n    /***/\n\n    const themeColor = fromTheme('color')\n    const themeFont = fromTheme('font')\n    const themeText = fromTheme('text')\n    const themeFontWeight = fromTheme('font-weight')\n    const themeTracking = fromTheme('tracking')\n    const themeLeading = fromTheme('leading')\n    const themeBreakpoint = fromTheme('breakpoint')\n    const themeContainer = fromTheme('container')\n    const themeSpacing = fromTheme('spacing')\n    const themeRadius = fromTheme('radius')\n    const themeShadow = fromTheme('shadow')\n    const themeInsetShadow = fromTheme('inset-shadow')\n    const themeTextShadow = fromTheme('text-shadow')\n    const themeDropShadow = fromTheme('drop-shadow')\n    const themeBlur = fromTheme('blur')\n    const themePerspective = fromTheme('perspective')\n    const themeAspect = fromTheme('aspect')\n    const themeEase = fromTheme('ease')\n    const themeAnimate = fromTheme('animate')\n\n    /**\n     * Helpers to avoid repeating the same scales\n     *\n     * We use functions that create a new array every time they're called instead of static arrays.\n     * This ensures that users who modify any scale by mutating the array (e.g. with `array.push(element)`) don't accidentally mutate arrays in other parts of the config.\n     */\n    /***/\n\n    const scaleBreak = () =>\n        ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'] as const\n    const scalePosition = () =>\n        [\n            'center',\n            'top',\n            'bottom',\n            'left',\n            'right',\n            'top-left',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'left-top',\n            'top-right',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'right-top',\n            'bottom-right',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'right-bottom',\n            'bottom-left',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'left-bottom',\n        ] as const\n    const scalePositionWithArbitrary = () =>\n        [...scalePosition(), isArbitraryVariable, isArbitraryValue] as const\n    const scaleOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'] as const\n    const scaleOverscroll = () => ['auto', 'contain', 'none'] as const\n    const scaleUnambiguousSpacing = () =>\n        [isArbitraryVariable, isArbitraryValue, themeSpacing] as const\n    const scaleInset = () => [isFraction, 'full', 'auto', ...scaleUnambiguousSpacing()] as const\n    const scaleGridTemplateColsRows = () =>\n        [isInteger, 'none', 'subgrid', isArbitraryVariable, isArbitraryValue] as const\n    const scaleGridColRowStartAndEnd = () =>\n        [\n            'auto',\n            { span: ['full', isInteger, isArbitraryVariable, isArbitraryValue] },\n            isInteger,\n            isArbitraryVariable,\n            isArbitraryValue,\n        ] as const\n    const scaleGridColRowStartOrEnd = () =>\n        [isInteger, 'auto', isArbitraryVariable, isArbitraryValue] as const\n    const scaleGridAutoColsRows = () =>\n        ['auto', 'min', 'max', 'fr', isArbitraryVariable, isArbitraryValue] as const\n    const scaleAlignPrimaryAxis = () =>\n        [\n            'start',\n            'end',\n            'center',\n            'between',\n            'around',\n            'evenly',\n            'stretch',\n            'baseline',\n            'center-safe',\n            'end-safe',\n        ] as const\n    const scaleAlignSecondaryAxis = () =>\n        ['start', 'end', 'center', 'stretch', 'center-safe', 'end-safe'] as const\n    const scaleMargin = () => ['auto', ...scaleUnambiguousSpacing()] as const\n    const scaleSizing = () =>\n        [\n            isFraction,\n            'auto',\n            'full',\n            'dvw',\n            'dvh',\n            'lvw',\n            'lvh',\n            'svw',\n            'svh',\n            'min',\n            'max',\n            'fit',\n            ...scaleUnambiguousSpacing(),\n        ] as const\n    const scaleColor = () => [themeColor, isArbitraryVariable, isArbitraryValue] as const\n    const scaleBgPosition = () =>\n        [\n            ...scalePosition(),\n            isArbitraryVariablePosition,\n            isArbitraryPosition,\n            { position: [isArbitraryVariable, isArbitraryValue] },\n        ] as const\n    const scaleBgRepeat = () => ['no-repeat', { repeat: ['', 'x', 'y', 'space', 'round'] }] as const\n    const scaleBgSize = () =>\n        [\n            'auto',\n            'cover',\n            'contain',\n            isArbitraryVariableSize,\n            isArbitrarySize,\n            { size: [isArbitraryVariable, isArbitraryValue] },\n        ] as const\n    const scaleGradientStopPosition = () =>\n        [isPercent, isArbitraryVariableLength, isArbitraryLength] as const\n    const scaleRadius = () =>\n        [\n            // Deprecated since Tailwind CSS v4.0.0\n            '',\n            'none',\n            'full',\n            themeRadius,\n            isArbitraryVariable,\n            isArbitraryValue,\n        ] as const\n    const scaleBorderWidth = () =>\n        ['', isNumber, isArbitraryVariableLength, isArbitraryLength] as const\n    const scaleLineStyle = () => ['solid', 'dashed', 'dotted', 'double'] as const\n    const scaleBlendMode = () =>\n        [\n            'normal',\n            'multiply',\n            'screen',\n            'overlay',\n            'darken',\n            'lighten',\n            'color-dodge',\n            'color-burn',\n            'hard-light',\n            'soft-light',\n            'difference',\n            'exclusion',\n            'hue',\n            'saturation',\n            'color',\n            'luminosity',\n        ] as const\n    const scaleMaskImagePosition = () =>\n        [isNumber, isPercent, isArbitraryVariablePosition, isArbitraryPosition] as const\n    const scaleBlur = () =>\n        [\n            // Deprecated since Tailwind CSS v4.0.0\n            '',\n            'none',\n            themeBlur,\n            isArbitraryVariable,\n            isArbitraryValue,\n        ] as const\n    const scaleRotate = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue] as const\n    const scaleScale = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue] as const\n    const scaleSkew = () => [isNumber, isArbitraryVariable, isArbitraryValue] as const\n    const scaleTranslate = () => [isFraction, 'full', ...scaleUnambiguousSpacing()] as const\n\n    return {\n        cacheSize: 500,\n        theme: {\n            animate: ['spin', 'ping', 'pulse', 'bounce'],\n            aspect: ['video'],\n            blur: [isTshirtSize],\n            breakpoint: [isTshirtSize],\n            color: [isAny],\n            container: [isTshirtSize],\n            'drop-shadow': [isTshirtSize],\n            ease: ['in', 'out', 'in-out'],\n            font: [isAnyNonArbitrary],\n            'font-weight': [\n                'thin',\n                'extralight',\n                'light',\n                'normal',\n                'medium',\n                'semibold',\n                'bold',\n                'extrabold',\n                'black',\n            ],\n            'inset-shadow': [isTshirtSize],\n            leading: ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose'],\n            perspective: ['dramatic', 'near', 'normal', 'midrange', 'distant', 'none'],\n            radius: [isTshirtSize],\n            shadow: [isTshirtSize],\n            spacing: ['px', isNumber],\n            text: [isTshirtSize],\n            'text-shadow': [isTshirtSize],\n            tracking: ['tighter', 'tight', 'normal', 'wide', 'wider', 'widest'],\n        },\n        classGroups: {\n            // --------------\n            // --- Layout ---\n            // --------------\n\n            /**\n             * Aspect Ratio\n             * @see https://tailwindcss.com/docs/aspect-ratio\n             */\n            aspect: [\n                {\n                    aspect: [\n                        'auto',\n                        'square',\n                        isFraction,\n                        isArbitraryValue,\n                        isArbitraryVariable,\n                        themeAspect,\n                    ],\n                },\n            ],\n            /**\n             * Container\n             * @see https://tailwindcss.com/docs/container\n             * @deprecated since Tailwind CSS v4.0.0\n             */\n            container: ['container'],\n            /**\n             * Columns\n             * @see https://tailwindcss.com/docs/columns\n             */\n            columns: [\n                { columns: [isNumber, isArbitraryValue, isArbitraryVariable, themeContainer] },\n            ],\n            /**\n             * Break After\n             * @see https://tailwindcss.com/docs/break-after\n             */\n            'break-after': [{ 'break-after': scaleBreak() }],\n            /**\n             * Break Before\n             * @see https://tailwindcss.com/docs/break-before\n             */\n            'break-before': [{ 'break-before': scaleBreak() }],\n            /**\n             * Break Inside\n             * @see https://tailwindcss.com/docs/break-inside\n             */\n            'break-inside': [{ 'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column'] }],\n            /**\n             * Box Decoration Break\n             * @see https://tailwindcss.com/docs/box-decoration-break\n             */\n            'box-decoration': [{ 'box-decoration': ['slice', 'clone'] }],\n            /**\n             * Box Sizing\n             * @see https://tailwindcss.com/docs/box-sizing\n             */\n            box: [{ box: ['border', 'content'] }],\n            /**\n             * Display\n             * @see https://tailwindcss.com/docs/display\n             */\n            display: [\n                'block',\n                'inline-block',\n                'inline',\n                'flex',\n                'inline-flex',\n                'table',\n                'inline-table',\n                'table-caption',\n                'table-cell',\n                'table-column',\n                'table-column-group',\n                'table-footer-group',\n                'table-header-group',\n                'table-row-group',\n                'table-row',\n                'flow-root',\n                'grid',\n                'inline-grid',\n                'contents',\n                'list-item',\n                'hidden',\n            ],\n            /**\n             * Screen Reader Only\n             * @see https://tailwindcss.com/docs/display#screen-reader-only\n             */\n            sr: ['sr-only', 'not-sr-only'],\n            /**\n             * Floats\n             * @see https://tailwindcss.com/docs/float\n             */\n            float: [{ float: ['right', 'left', 'none', 'start', 'end'] }],\n            /**\n             * Clear\n             * @see https://tailwindcss.com/docs/clear\n             */\n            clear: [{ clear: ['left', 'right', 'both', 'none', 'start', 'end'] }],\n            /**\n             * Isolation\n             * @see https://tailwindcss.com/docs/isolation\n             */\n            isolation: ['isolate', 'isolation-auto'],\n            /**\n             * Object Fit\n             * @see https://tailwindcss.com/docs/object-fit\n             */\n            'object-fit': [{ object: ['contain', 'cover', 'fill', 'none', 'scale-down'] }],\n            /**\n             * Object Position\n             * @see https://tailwindcss.com/docs/object-position\n             */\n            'object-position': [{ object: scalePositionWithArbitrary() }],\n            /**\n             * Overflow\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            overflow: [{ overflow: scaleOverflow() }],\n            /**\n             * Overflow X\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            'overflow-x': [{ 'overflow-x': scaleOverflow() }],\n            /**\n             * Overflow Y\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            'overflow-y': [{ 'overflow-y': scaleOverflow() }],\n            /**\n             * Overscroll Behavior\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            overscroll: [{ overscroll: scaleOverscroll() }],\n            /**\n             * Overscroll Behavior X\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            'overscroll-x': [{ 'overscroll-x': scaleOverscroll() }],\n            /**\n             * Overscroll Behavior Y\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            'overscroll-y': [{ 'overscroll-y': scaleOverscroll() }],\n            /**\n             * Position\n             * @see https://tailwindcss.com/docs/position\n             */\n            position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n            /**\n             * Top / Right / Bottom / Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            inset: [{ inset: scaleInset() }],\n            /**\n             * Right / Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            'inset-x': [{ 'inset-x': scaleInset() }],\n            /**\n             * Top / Bottom\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            'inset-y': [{ 'inset-y': scaleInset() }],\n            /**\n             * Start\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            start: [{ start: scaleInset() }],\n            /**\n             * End\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            end: [{ end: scaleInset() }],\n            /**\n             * Top\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            top: [{ top: scaleInset() }],\n            /**\n             * Right\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            right: [{ right: scaleInset() }],\n            /**\n             * Bottom\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            bottom: [{ bottom: scaleInset() }],\n            /**\n             * Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            left: [{ left: scaleInset() }],\n            /**\n             * Visibility\n             * @see https://tailwindcss.com/docs/visibility\n             */\n            visibility: ['visible', 'invisible', 'collapse'],\n            /**\n             * Z-Index\n             * @see https://tailwindcss.com/docs/z-index\n             */\n            z: [{ z: [isInteger, 'auto', isArbitraryVariable, isArbitraryValue] }],\n\n            // ------------------------\n            // --- Flexbox and Grid ---\n            // ------------------------\n\n            /**\n             * Flex Basis\n             * @see https://tailwindcss.com/docs/flex-basis\n             */\n            basis: [\n                {\n                    basis: [\n                        isFraction,\n                        'full',\n                        'auto',\n                        themeContainer,\n                        ...scaleUnambiguousSpacing(),\n                    ],\n                },\n            ],\n            /**\n             * Flex Direction\n             * @see https://tailwindcss.com/docs/flex-direction\n             */\n            'flex-direction': [{ flex: ['row', 'row-reverse', 'col', 'col-reverse'] }],\n            /**\n             * Flex Wrap\n             * @see https://tailwindcss.com/docs/flex-wrap\n             */\n            'flex-wrap': [{ flex: ['nowrap', 'wrap', 'wrap-reverse'] }],\n            /**\n             * Flex\n             * @see https://tailwindcss.com/docs/flex\n             */\n            flex: [{ flex: [isNumber, isFraction, 'auto', 'initial', 'none', isArbitraryValue] }],\n            /**\n             * Flex Grow\n             * @see https://tailwindcss.com/docs/flex-grow\n             */\n            grow: [{ grow: ['', isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Flex Shrink\n             * @see https://tailwindcss.com/docs/flex-shrink\n             */\n            shrink: [{ shrink: ['', isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Order\n             * @see https://tailwindcss.com/docs/order\n             */\n            order: [\n                {\n                    order: [\n                        isInteger,\n                        'first',\n                        'last',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Grid Template Columns\n             * @see https://tailwindcss.com/docs/grid-template-columns\n             */\n            'grid-cols': [{ 'grid-cols': scaleGridTemplateColsRows() }],\n            /**\n             * Grid Column Start / End\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-start-end': [{ col: scaleGridColRowStartAndEnd() }],\n            /**\n             * Grid Column Start\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-start': [{ 'col-start': scaleGridColRowStartOrEnd() }],\n            /**\n             * Grid Column End\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-end': [{ 'col-end': scaleGridColRowStartOrEnd() }],\n            /**\n             * Grid Template Rows\n             * @see https://tailwindcss.com/docs/grid-template-rows\n             */\n            'grid-rows': [{ 'grid-rows': scaleGridTemplateColsRows() }],\n            /**\n             * Grid Row Start / End\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-start-end': [{ row: scaleGridColRowStartAndEnd() }],\n            /**\n             * Grid Row Start\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-start': [{ 'row-start': scaleGridColRowStartOrEnd() }],\n            /**\n             * Grid Row End\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-end': [{ 'row-end': scaleGridColRowStartOrEnd() }],\n            /**\n             * Grid Auto Flow\n             * @see https://tailwindcss.com/docs/grid-auto-flow\n             */\n            'grid-flow': [{ 'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense'] }],\n            /**\n             * Grid Auto Columns\n             * @see https://tailwindcss.com/docs/grid-auto-columns\n             */\n            'auto-cols': [{ 'auto-cols': scaleGridAutoColsRows() }],\n            /**\n             * Grid Auto Rows\n             * @see https://tailwindcss.com/docs/grid-auto-rows\n             */\n            'auto-rows': [{ 'auto-rows': scaleGridAutoColsRows() }],\n            /**\n             * Gap\n             * @see https://tailwindcss.com/docs/gap\n             */\n            gap: [{ gap: scaleUnambiguousSpacing() }],\n            /**\n             * Gap X\n             * @see https://tailwindcss.com/docs/gap\n             */\n            'gap-x': [{ 'gap-x': scaleUnambiguousSpacing() }],\n            /**\n             * Gap Y\n             * @see https://tailwindcss.com/docs/gap\n             */\n            'gap-y': [{ 'gap-y': scaleUnambiguousSpacing() }],\n            /**\n             * Justify Content\n             * @see https://tailwindcss.com/docs/justify-content\n             */\n            'justify-content': [{ justify: [...scaleAlignPrimaryAxis(), 'normal'] }],\n            /**\n             * Justify Items\n             * @see https://tailwindcss.com/docs/justify-items\n             */\n            'justify-items': [{ 'justify-items': [...scaleAlignSecondaryAxis(), 'normal'] }],\n            /**\n             * Justify Self\n             * @see https://tailwindcss.com/docs/justify-self\n             */\n            'justify-self': [{ 'justify-self': ['auto', ...scaleAlignSecondaryAxis()] }],\n            /**\n             * Align Content\n             * @see https://tailwindcss.com/docs/align-content\n             */\n            'align-content': [{ content: ['normal', ...scaleAlignPrimaryAxis()] }],\n            /**\n             * Align Items\n             * @see https://tailwindcss.com/docs/align-items\n             */\n            'align-items': [{ items: [...scaleAlignSecondaryAxis(), { baseline: ['', 'last'] }] }],\n            /**\n             * Align Self\n             * @see https://tailwindcss.com/docs/align-self\n             */\n            'align-self': [\n                { self: ['auto', ...scaleAlignSecondaryAxis(), { baseline: ['', 'last'] }] },\n            ],\n            /**\n             * Place Content\n             * @see https://tailwindcss.com/docs/place-content\n             */\n            'place-content': [{ 'place-content': scaleAlignPrimaryAxis() }],\n            /**\n             * Place Items\n             * @see https://tailwindcss.com/docs/place-items\n             */\n            'place-items': [{ 'place-items': [...scaleAlignSecondaryAxis(), 'baseline'] }],\n            /**\n             * Place Self\n             * @see https://tailwindcss.com/docs/place-self\n             */\n            'place-self': [{ 'place-self': ['auto', ...scaleAlignSecondaryAxis()] }],\n            // Spacing\n            /**\n             * Padding\n             * @see https://tailwindcss.com/docs/padding\n             */\n            p: [{ p: scaleUnambiguousSpacing() }],\n            /**\n             * Padding X\n             * @see https://tailwindcss.com/docs/padding\n             */\n            px: [{ px: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Y\n             * @see https://tailwindcss.com/docs/padding\n             */\n            py: [{ py: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Start\n             * @see https://tailwindcss.com/docs/padding\n             */\n            ps: [{ ps: scaleUnambiguousSpacing() }],\n            /**\n             * Padding End\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pe: [{ pe: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Top\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pt: [{ pt: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Right\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pr: [{ pr: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Bottom\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pb: [{ pb: scaleUnambiguousSpacing() }],\n            /**\n             * Padding Left\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pl: [{ pl: scaleUnambiguousSpacing() }],\n            /**\n             * Margin\n             * @see https://tailwindcss.com/docs/margin\n             */\n            m: [{ m: scaleMargin() }],\n            /**\n             * Margin X\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mx: [{ mx: scaleMargin() }],\n            /**\n             * Margin Y\n             * @see https://tailwindcss.com/docs/margin\n             */\n            my: [{ my: scaleMargin() }],\n            /**\n             * Margin Start\n             * @see https://tailwindcss.com/docs/margin\n             */\n            ms: [{ ms: scaleMargin() }],\n            /**\n             * Margin End\n             * @see https://tailwindcss.com/docs/margin\n             */\n            me: [{ me: scaleMargin() }],\n            /**\n             * Margin Top\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mt: [{ mt: scaleMargin() }],\n            /**\n             * Margin Right\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mr: [{ mr: scaleMargin() }],\n            /**\n             * Margin Bottom\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mb: [{ mb: scaleMargin() }],\n            /**\n             * Margin Left\n             * @see https://tailwindcss.com/docs/margin\n             */\n            ml: [{ ml: scaleMargin() }],\n            /**\n             * Space Between X\n             * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n             */\n            'space-x': [{ 'space-x': scaleUnambiguousSpacing() }],\n            /**\n             * Space Between X Reverse\n             * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n             */\n            'space-x-reverse': ['space-x-reverse'],\n            /**\n             * Space Between Y\n             * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n             */\n            'space-y': [{ 'space-y': scaleUnambiguousSpacing() }],\n            /**\n             * Space Between Y Reverse\n             * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n             */\n            'space-y-reverse': ['space-y-reverse'],\n\n            // --------------\n            // --- Sizing ---\n            // --------------\n\n            /**\n             * Size\n             * @see https://tailwindcss.com/docs/width#setting-both-width-and-height\n             */\n            size: [{ size: scaleSizing() }],\n            /**\n             * Width\n             * @see https://tailwindcss.com/docs/width\n             */\n            w: [{ w: [themeContainer, 'screen', ...scaleSizing()] }],\n            /**\n             * Min-Width\n             * @see https://tailwindcss.com/docs/min-width\n             */\n            'min-w': [\n                {\n                    'min-w': [\n                        themeContainer,\n                        'screen',\n                        /** Deprecated. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n                        'none',\n                        ...scaleSizing(),\n                    ],\n                },\n            ],\n            /**\n             * Max-Width\n             * @see https://tailwindcss.com/docs/max-width\n             */\n            'max-w': [\n                {\n                    'max-w': [\n                        themeContainer,\n                        'screen',\n                        'none',\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n                        'prose',\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n                        { screen: [themeBreakpoint] },\n                        ...scaleSizing(),\n                    ],\n                },\n            ],\n            /**\n             * Height\n             * @see https://tailwindcss.com/docs/height\n             */\n            h: [{ h: ['screen', 'lh', ...scaleSizing()] }],\n            /**\n             * Min-Height\n             * @see https://tailwindcss.com/docs/min-height\n             */\n            'min-h': [{ 'min-h': ['screen', 'lh', 'none', ...scaleSizing()] }],\n            /**\n             * Max-Height\n             * @see https://tailwindcss.com/docs/max-height\n             */\n            'max-h': [{ 'max-h': ['screen', 'lh', ...scaleSizing()] }],\n\n            // ------------------\n            // --- Typography ---\n            // ------------------\n\n            /**\n             * Font Size\n             * @see https://tailwindcss.com/docs/font-size\n             */\n            'font-size': [\n                { text: ['base', themeText, isArbitraryVariableLength, isArbitraryLength] },\n            ],\n            /**\n             * Font Smoothing\n             * @see https://tailwindcss.com/docs/font-smoothing\n             */\n            'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n            /**\n             * Font Style\n             * @see https://tailwindcss.com/docs/font-style\n             */\n            'font-style': ['italic', 'not-italic'],\n            /**\n             * Font Weight\n             * @see https://tailwindcss.com/docs/font-weight\n             */\n            'font-weight': [{ font: [themeFontWeight, isArbitraryVariable, isArbitraryNumber] }],\n            /**\n             * Font Stretch\n             * @see https://tailwindcss.com/docs/font-stretch\n             */\n            'font-stretch': [\n                {\n                    'font-stretch': [\n                        'ultra-condensed',\n                        'extra-condensed',\n                        'condensed',\n                        'semi-condensed',\n                        'normal',\n                        'semi-expanded',\n                        'expanded',\n                        'extra-expanded',\n                        'ultra-expanded',\n                        isPercent,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Font Family\n             * @see https://tailwindcss.com/docs/font-family\n             */\n            'font-family': [{ font: [isArbitraryVariableFamilyName, isArbitraryValue, themeFont] }],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-normal': ['normal-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-ordinal': ['ordinal'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-slashed-zero': ['slashed-zero'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-fraction': ['diagonal-fractions', 'stacked-fractions'],\n            /**\n             * Letter Spacing\n             * @see https://tailwindcss.com/docs/letter-spacing\n             */\n            tracking: [{ tracking: [themeTracking, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Line Clamp\n             * @see https://tailwindcss.com/docs/line-clamp\n             */\n            'line-clamp': [\n                { 'line-clamp': [isNumber, 'none', isArbitraryVariable, isArbitraryNumber] },\n            ],\n            /**\n             * Line Height\n             * @see https://tailwindcss.com/docs/line-height\n             */\n            leading: [\n                {\n                    leading: [\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n                        themeLeading,\n                        ...scaleUnambiguousSpacing(),\n                    ],\n                },\n            ],\n            /**\n             * List Style Image\n             * @see https://tailwindcss.com/docs/list-style-image\n             */\n            'list-image': [{ 'list-image': ['none', isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * List Style Position\n             * @see https://tailwindcss.com/docs/list-style-position\n             */\n            'list-style-position': [{ list: ['inside', 'outside'] }],\n            /**\n             * List Style Type\n             * @see https://tailwindcss.com/docs/list-style-type\n             */\n            'list-style-type': [\n                { list: ['disc', 'decimal', 'none', isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Text Alignment\n             * @see https://tailwindcss.com/docs/text-align\n             */\n            'text-alignment': [{ text: ['left', 'center', 'right', 'justify', 'start', 'end'] }],\n            /**\n             * Placeholder Color\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://v3.tailwindcss.com/docs/placeholder-color\n             */\n            'placeholder-color': [{ placeholder: scaleColor() }],\n            /**\n             * Text Color\n             * @see https://tailwindcss.com/docs/text-color\n             */\n            'text-color': [{ text: scaleColor() }],\n            /**\n             * Text Decoration\n             * @see https://tailwindcss.com/docs/text-decoration\n             */\n            'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n            /**\n             * Text Decoration Style\n             * @see https://tailwindcss.com/docs/text-decoration-style\n             */\n            'text-decoration-style': [{ decoration: [...scaleLineStyle(), 'wavy'] }],\n            /**\n             * Text Decoration Thickness\n             * @see https://tailwindcss.com/docs/text-decoration-thickness\n             */\n            'text-decoration-thickness': [\n                {\n                    decoration: [\n                        isNumber,\n                        'from-font',\n                        'auto',\n                        isArbitraryVariable,\n                        isArbitraryLength,\n                    ],\n                },\n            ],\n            /**\n             * Text Decoration Color\n             * @see https://tailwindcss.com/docs/text-decoration-color\n             */\n            'text-decoration-color': [{ decoration: scaleColor() }],\n            /**\n             * Text Underline Offset\n             * @see https://tailwindcss.com/docs/text-underline-offset\n             */\n            'underline-offset': [\n                { 'underline-offset': [isNumber, 'auto', isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Text Transform\n             * @see https://tailwindcss.com/docs/text-transform\n             */\n            'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n            /**\n             * Text Overflow\n             * @see https://tailwindcss.com/docs/text-overflow\n             */\n            'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n            /**\n             * Text Wrap\n             * @see https://tailwindcss.com/docs/text-wrap\n             */\n            'text-wrap': [{ text: ['wrap', 'nowrap', 'balance', 'pretty'] }],\n            /**\n             * Text Indent\n             * @see https://tailwindcss.com/docs/text-indent\n             */\n            indent: [{ indent: scaleUnambiguousSpacing() }],\n            /**\n             * Vertical Alignment\n             * @see https://tailwindcss.com/docs/vertical-align\n             */\n            'vertical-align': [\n                {\n                    align: [\n                        'baseline',\n                        'top',\n                        'middle',\n                        'bottom',\n                        'text-top',\n                        'text-bottom',\n                        'sub',\n                        'super',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Whitespace\n             * @see https://tailwindcss.com/docs/whitespace\n             */\n            whitespace: [\n                { whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces'] },\n            ],\n            /**\n             * Word Break\n             * @see https://tailwindcss.com/docs/word-break\n             */\n            break: [{ break: ['normal', 'words', 'all', 'keep'] }],\n            /**\n             * Overflow Wrap\n             * @see https://tailwindcss.com/docs/overflow-wrap\n             */\n            wrap: [{ wrap: ['break-word', 'anywhere', 'normal'] }],\n            /**\n             * Hyphens\n             * @see https://tailwindcss.com/docs/hyphens\n             */\n            hyphens: [{ hyphens: ['none', 'manual', 'auto'] }],\n            /**\n             * Content\n             * @see https://tailwindcss.com/docs/content\n             */\n            content: [{ content: ['none', isArbitraryVariable, isArbitraryValue] }],\n\n            // -------------------\n            // --- Backgrounds ---\n            // -------------------\n\n            /**\n             * Background Attachment\n             * @see https://tailwindcss.com/docs/background-attachment\n             */\n            'bg-attachment': [{ bg: ['fixed', 'local', 'scroll'] }],\n            /**\n             * Background Clip\n             * @see https://tailwindcss.com/docs/background-clip\n             */\n            'bg-clip': [{ 'bg-clip': ['border', 'padding', 'content', 'text'] }],\n            /**\n             * Background Origin\n             * @see https://tailwindcss.com/docs/background-origin\n             */\n            'bg-origin': [{ 'bg-origin': ['border', 'padding', 'content'] }],\n            /**\n             * Background Position\n             * @see https://tailwindcss.com/docs/background-position\n             */\n            'bg-position': [{ bg: scaleBgPosition() }],\n            /**\n             * Background Repeat\n             * @see https://tailwindcss.com/docs/background-repeat\n             */\n            'bg-repeat': [{ bg: scaleBgRepeat() }],\n            /**\n             * Background Size\n             * @see https://tailwindcss.com/docs/background-size\n             */\n            'bg-size': [{ bg: scaleBgSize() }],\n            /**\n             * Background Image\n             * @see https://tailwindcss.com/docs/background-image\n             */\n            'bg-image': [\n                {\n                    bg: [\n                        'none',\n                        {\n                            linear: [\n                                { to: ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl'] },\n                                isInteger,\n                                isArbitraryVariable,\n                                isArbitraryValue,\n                            ],\n                            radial: ['', isArbitraryVariable, isArbitraryValue],\n                            conic: [isInteger, isArbitraryVariable, isArbitraryValue],\n                        },\n                        isArbitraryVariableImage,\n                        isArbitraryImage,\n                    ],\n                },\n            ],\n            /**\n             * Background Color\n             * @see https://tailwindcss.com/docs/background-color\n             */\n            'bg-color': [{ bg: scaleColor() }],\n            /**\n             * Gradient Color Stops From Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-from-pos': [{ from: scaleGradientStopPosition() }],\n            /**\n             * Gradient Color Stops Via Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-via-pos': [{ via: scaleGradientStopPosition() }],\n            /**\n             * Gradient Color Stops To Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-to-pos': [{ to: scaleGradientStopPosition() }],\n            /**\n             * Gradient Color Stops From\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-from': [{ from: scaleColor() }],\n            /**\n             * Gradient Color Stops Via\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-via': [{ via: scaleColor() }],\n            /**\n             * Gradient Color Stops To\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-to': [{ to: scaleColor() }],\n\n            // ---------------\n            // --- Borders ---\n            // ---------------\n\n            /**\n             * Border Radius\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            rounded: [{ rounded: scaleRadius() }],\n            /**\n             * Border Radius Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-s': [{ 'rounded-s': scaleRadius() }],\n            /**\n             * Border Radius End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-e': [{ 'rounded-e': scaleRadius() }],\n            /**\n             * Border Radius Top\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-t': [{ 'rounded-t': scaleRadius() }],\n            /**\n             * Border Radius Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-r': [{ 'rounded-r': scaleRadius() }],\n            /**\n             * Border Radius Bottom\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-b': [{ 'rounded-b': scaleRadius() }],\n            /**\n             * Border Radius Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-l': [{ 'rounded-l': scaleRadius() }],\n            /**\n             * Border Radius Start Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-ss': [{ 'rounded-ss': scaleRadius() }],\n            /**\n             * Border Radius Start End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-se': [{ 'rounded-se': scaleRadius() }],\n            /**\n             * Border Radius End End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-ee': [{ 'rounded-ee': scaleRadius() }],\n            /**\n             * Border Radius End Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-es': [{ 'rounded-es': scaleRadius() }],\n            /**\n             * Border Radius Top Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-tl': [{ 'rounded-tl': scaleRadius() }],\n            /**\n             * Border Radius Top Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-tr': [{ 'rounded-tr': scaleRadius() }],\n            /**\n             * Border Radius Bottom Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-br': [{ 'rounded-br': scaleRadius() }],\n            /**\n             * Border Radius Bottom Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-bl': [{ 'rounded-bl': scaleRadius() }],\n            /**\n             * Border Width\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w': [{ border: scaleBorderWidth() }],\n            /**\n             * Border Width X\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-x': [{ 'border-x': scaleBorderWidth() }],\n            /**\n             * Border Width Y\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-y': [{ 'border-y': scaleBorderWidth() }],\n            /**\n             * Border Width Start\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-s': [{ 'border-s': scaleBorderWidth() }],\n            /**\n             * Border Width End\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-e': [{ 'border-e': scaleBorderWidth() }],\n            /**\n             * Border Width Top\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-t': [{ 'border-t': scaleBorderWidth() }],\n            /**\n             * Border Width Right\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-r': [{ 'border-r': scaleBorderWidth() }],\n            /**\n             * Border Width Bottom\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-b': [{ 'border-b': scaleBorderWidth() }],\n            /**\n             * Border Width Left\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-l': [{ 'border-l': scaleBorderWidth() }],\n            /**\n             * Divide Width X\n             * @see https://tailwindcss.com/docs/border-width#between-children\n             */\n            'divide-x': [{ 'divide-x': scaleBorderWidth() }],\n            /**\n             * Divide Width X Reverse\n             * @see https://tailwindcss.com/docs/border-width#between-children\n             */\n            'divide-x-reverse': ['divide-x-reverse'],\n            /**\n             * Divide Width Y\n             * @see https://tailwindcss.com/docs/border-width#between-children\n             */\n            'divide-y': [{ 'divide-y': scaleBorderWidth() }],\n            /**\n             * Divide Width Y Reverse\n             * @see https://tailwindcss.com/docs/border-width#between-children\n             */\n            'divide-y-reverse': ['divide-y-reverse'],\n            /**\n             * Border Style\n             * @see https://tailwindcss.com/docs/border-style\n             */\n            'border-style': [{ border: [...scaleLineStyle(), 'hidden', 'none'] }],\n            /**\n             * Divide Style\n             * @see https://tailwindcss.com/docs/border-style#setting-the-divider-style\n             */\n            'divide-style': [{ divide: [...scaleLineStyle(), 'hidden', 'none'] }],\n            /**\n             * Border Color\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color': [{ border: scaleColor() }],\n            /**\n             * Border Color X\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-x': [{ 'border-x': scaleColor() }],\n            /**\n             * Border Color Y\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-y': [{ 'border-y': scaleColor() }],\n            /**\n             * Border Color S\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-s': [{ 'border-s': scaleColor() }],\n            /**\n             * Border Color E\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-e': [{ 'border-e': scaleColor() }],\n            /**\n             * Border Color Top\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-t': [{ 'border-t': scaleColor() }],\n            /**\n             * Border Color Right\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-r': [{ 'border-r': scaleColor() }],\n            /**\n             * Border Color Bottom\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-b': [{ 'border-b': scaleColor() }],\n            /**\n             * Border Color Left\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-l': [{ 'border-l': scaleColor() }],\n            /**\n             * Divide Color\n             * @see https://tailwindcss.com/docs/divide-color\n             */\n            'divide-color': [{ divide: scaleColor() }],\n            /**\n             * Outline Style\n             * @see https://tailwindcss.com/docs/outline-style\n             */\n            'outline-style': [{ outline: [...scaleLineStyle(), 'none', 'hidden'] }],\n            /**\n             * Outline Offset\n             * @see https://tailwindcss.com/docs/outline-offset\n             */\n            'outline-offset': [\n                { 'outline-offset': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Outline Width\n             * @see https://tailwindcss.com/docs/outline-width\n             */\n            'outline-w': [\n                { outline: ['', isNumber, isArbitraryVariableLength, isArbitraryLength] },\n            ],\n            /**\n             * Outline Color\n             * @see https://tailwindcss.com/docs/outline-color\n             */\n            'outline-color': [{ outline: scaleColor() }],\n\n            // ---------------\n            // --- Effects ---\n            // ---------------\n\n            /**\n             * Box Shadow\n             * @see https://tailwindcss.com/docs/box-shadow\n             */\n            shadow: [\n                {\n                    shadow: [\n                        // Deprecated since Tailwind CSS v4.0.0\n                        '',\n                        'none',\n                        themeShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow,\n                    ],\n                },\n            ],\n            /**\n             * Box Shadow Color\n             * @see https://tailwindcss.com/docs/box-shadow#setting-the-shadow-color\n             */\n            'shadow-color': [{ shadow: scaleColor() }],\n            /**\n             * Inset Box Shadow\n             * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-shadow\n             */\n            'inset-shadow': [\n                {\n                    'inset-shadow': [\n                        'none',\n                        themeInsetShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow,\n                    ],\n                },\n            ],\n            /**\n             * Inset Box Shadow Color\n             * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-shadow-color\n             */\n            'inset-shadow-color': [{ 'inset-shadow': scaleColor() }],\n            /**\n             * Ring Width\n             * @see https://tailwindcss.com/docs/box-shadow#adding-a-ring\n             */\n            'ring-w': [{ ring: scaleBorderWidth() }],\n            /**\n             * Ring Width Inset\n             * @see https://v3.tailwindcss.com/docs/ring-width#inset-rings\n             * @deprecated since Tailwind CSS v4.0.0\n             * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n             */\n            'ring-w-inset': ['ring-inset'],\n            /**\n             * Ring Color\n             * @see https://tailwindcss.com/docs/box-shadow#setting-the-ring-color\n             */\n            'ring-color': [{ ring: scaleColor() }],\n            /**\n             * Ring Offset Width\n             * @see https://v3.tailwindcss.com/docs/ring-offset-width\n             * @deprecated since Tailwind CSS v4.0.0\n             * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n             */\n            'ring-offset-w': [{ 'ring-offset': [isNumber, isArbitraryLength] }],\n            /**\n             * Ring Offset Color\n             * @see https://v3.tailwindcss.com/docs/ring-offset-color\n             * @deprecated since Tailwind CSS v4.0.0\n             * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n             */\n            'ring-offset-color': [{ 'ring-offset': scaleColor() }],\n            /**\n             * Inset Ring Width\n             * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-ring\n             */\n            'inset-ring-w': [{ 'inset-ring': scaleBorderWidth() }],\n            /**\n             * Inset Ring Color\n             * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-ring-color\n             */\n            'inset-ring-color': [{ 'inset-ring': scaleColor() }],\n            /**\n             * Text Shadow\n             * @see https://tailwindcss.com/docs/text-shadow\n             */\n            'text-shadow': [\n                {\n                    'text-shadow': [\n                        'none',\n                        themeTextShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow,\n                    ],\n                },\n            ],\n            /**\n             * Text Shadow Color\n             * @see https://tailwindcss.com/docs/text-shadow#setting-the-shadow-color\n             */\n            'text-shadow-color': [{ 'text-shadow': scaleColor() }],\n            /**\n             * Opacity\n             * @see https://tailwindcss.com/docs/opacity\n             */\n            opacity: [{ opacity: [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Mix Blend Mode\n             * @see https://tailwindcss.com/docs/mix-blend-mode\n             */\n            'mix-blend': [{ 'mix-blend': [...scaleBlendMode(), 'plus-darker', 'plus-lighter'] }],\n            /**\n             * Background Blend Mode\n             * @see https://tailwindcss.com/docs/background-blend-mode\n             */\n            'bg-blend': [{ 'bg-blend': scaleBlendMode() }],\n            /**\n             * Mask Clip\n             * @see https://tailwindcss.com/docs/mask-clip\n             */\n            'mask-clip': [\n                { 'mask-clip': ['border', 'padding', 'content', 'fill', 'stroke', 'view'] },\n                'mask-no-clip',\n            ],\n            /**\n             * Mask Composite\n             * @see https://tailwindcss.com/docs/mask-composite\n             */\n            'mask-composite': [{ mask: ['add', 'subtract', 'intersect', 'exclude'] }],\n            /**\n             * Mask Image\n             * @see https://tailwindcss.com/docs/mask-image\n             */\n            'mask-image-linear-pos': [{ 'mask-linear': [isNumber] }],\n            'mask-image-linear-from-pos': [{ 'mask-linear-from': scaleMaskImagePosition() }],\n            'mask-image-linear-to-pos': [{ 'mask-linear-to': scaleMaskImagePosition() }],\n            'mask-image-linear-from-color': [{ 'mask-linear-from': scaleColor() }],\n            'mask-image-linear-to-color': [{ 'mask-linear-to': scaleColor() }],\n            'mask-image-t-from-pos': [{ 'mask-t-from': scaleMaskImagePosition() }],\n            'mask-image-t-to-pos': [{ 'mask-t-to': scaleMaskImagePosition() }],\n            'mask-image-t-from-color': [{ 'mask-t-from': scaleColor() }],\n            'mask-image-t-to-color': [{ 'mask-t-to': scaleColor() }],\n            'mask-image-r-from-pos': [{ 'mask-r-from': scaleMaskImagePosition() }],\n            'mask-image-r-to-pos': [{ 'mask-r-to': scaleMaskImagePosition() }],\n            'mask-image-r-from-color': [{ 'mask-r-from': scaleColor() }],\n            'mask-image-r-to-color': [{ 'mask-r-to': scaleColor() }],\n            'mask-image-b-from-pos': [{ 'mask-b-from': scaleMaskImagePosition() }],\n            'mask-image-b-to-pos': [{ 'mask-b-to': scaleMaskImagePosition() }],\n            'mask-image-b-from-color': [{ 'mask-b-from': scaleColor() }],\n            'mask-image-b-to-color': [{ 'mask-b-to': scaleColor() }],\n            'mask-image-l-from-pos': [{ 'mask-l-from': scaleMaskImagePosition() }],\n            'mask-image-l-to-pos': [{ 'mask-l-to': scaleMaskImagePosition() }],\n            'mask-image-l-from-color': [{ 'mask-l-from': scaleColor() }],\n            'mask-image-l-to-color': [{ 'mask-l-to': scaleColor() }],\n            'mask-image-x-from-pos': [{ 'mask-x-from': scaleMaskImagePosition() }],\n            'mask-image-x-to-pos': [{ 'mask-x-to': scaleMaskImagePosition() }],\n            'mask-image-x-from-color': [{ 'mask-x-from': scaleColor() }],\n            'mask-image-x-to-color': [{ 'mask-x-to': scaleColor() }],\n            'mask-image-y-from-pos': [{ 'mask-y-from': scaleMaskImagePosition() }],\n            'mask-image-y-to-pos': [{ 'mask-y-to': scaleMaskImagePosition() }],\n            'mask-image-y-from-color': [{ 'mask-y-from': scaleColor() }],\n            'mask-image-y-to-color': [{ 'mask-y-to': scaleColor() }],\n            'mask-image-radial': [{ 'mask-radial': [isArbitraryVariable, isArbitraryValue] }],\n            'mask-image-radial-from-pos': [{ 'mask-radial-from': scaleMaskImagePosition() }],\n            'mask-image-radial-to-pos': [{ 'mask-radial-to': scaleMaskImagePosition() }],\n            'mask-image-radial-from-color': [{ 'mask-radial-from': scaleColor() }],\n            'mask-image-radial-to-color': [{ 'mask-radial-to': scaleColor() }],\n            'mask-image-radial-shape': [{ 'mask-radial': ['circle', 'ellipse'] }],\n            'mask-image-radial-size': [\n                { 'mask-radial': [{ closest: ['side', 'corner'], farthest: ['side', 'corner'] }] },\n            ],\n            'mask-image-radial-pos': [{ 'mask-radial-at': scalePosition() }],\n            'mask-image-conic-pos': [{ 'mask-conic': [isNumber] }],\n            'mask-image-conic-from-pos': [{ 'mask-conic-from': scaleMaskImagePosition() }],\n            'mask-image-conic-to-pos': [{ 'mask-conic-to': scaleMaskImagePosition() }],\n            'mask-image-conic-from-color': [{ 'mask-conic-from': scaleColor() }],\n            'mask-image-conic-to-color': [{ 'mask-conic-to': scaleColor() }],\n            /**\n             * Mask Mode\n             * @see https://tailwindcss.com/docs/mask-mode\n             */\n            'mask-mode': [{ mask: ['alpha', 'luminance', 'match'] }],\n            /**\n             * Mask Origin\n             * @see https://tailwindcss.com/docs/mask-origin\n             */\n            'mask-origin': [\n                { 'mask-origin': ['border', 'padding', 'content', 'fill', 'stroke', 'view'] },\n            ],\n            /**\n             * Mask Position\n             * @see https://tailwindcss.com/docs/mask-position\n             */\n            'mask-position': [{ mask: scaleBgPosition() }],\n            /**\n             * Mask Repeat\n             * @see https://tailwindcss.com/docs/mask-repeat\n             */\n            'mask-repeat': [{ mask: scaleBgRepeat() }],\n            /**\n             * Mask Size\n             * @see https://tailwindcss.com/docs/mask-size\n             */\n            'mask-size': [{ mask: scaleBgSize() }],\n            /**\n             * Mask Type\n             * @see https://tailwindcss.com/docs/mask-type\n             */\n            'mask-type': [{ 'mask-type': ['alpha', 'luminance'] }],\n            /**\n             * Mask Image\n             * @see https://tailwindcss.com/docs/mask-image\n             */\n            'mask-image': [{ mask: ['none', isArbitraryVariable, isArbitraryValue] }],\n\n            // ---------------\n            // --- Filters ---\n            // ---------------\n\n            /**\n             * Filter\n             * @see https://tailwindcss.com/docs/filter\n             */\n            filter: [\n                {\n                    filter: [\n                        // Deprecated since Tailwind CSS v3.0.0\n                        '',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Blur\n             * @see https://tailwindcss.com/docs/blur\n             */\n            blur: [{ blur: scaleBlur() }],\n            /**\n             * Brightness\n             * @see https://tailwindcss.com/docs/brightness\n             */\n            brightness: [{ brightness: [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Contrast\n             * @see https://tailwindcss.com/docs/contrast\n             */\n            contrast: [{ contrast: [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Drop Shadow\n             * @see https://tailwindcss.com/docs/drop-shadow\n             */\n            'drop-shadow': [\n                {\n                    'drop-shadow': [\n                        // Deprecated since Tailwind CSS v4.0.0\n                        '',\n                        'none',\n                        themeDropShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow,\n                    ],\n                },\n            ],\n            /**\n             * Drop Shadow Color\n             * @see https://tailwindcss.com/docs/filter-drop-shadow#setting-the-shadow-color\n             */\n            'drop-shadow-color': [{ 'drop-shadow': scaleColor() }],\n            /**\n             * Grayscale\n             * @see https://tailwindcss.com/docs/grayscale\n             */\n            grayscale: [{ grayscale: ['', isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Hue Rotate\n             * @see https://tailwindcss.com/docs/hue-rotate\n             */\n            'hue-rotate': [{ 'hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Invert\n             * @see https://tailwindcss.com/docs/invert\n             */\n            invert: [{ invert: ['', isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Saturate\n             * @see https://tailwindcss.com/docs/saturate\n             */\n            saturate: [{ saturate: [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Sepia\n             * @see https://tailwindcss.com/docs/sepia\n             */\n            sepia: [{ sepia: ['', isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Backdrop Filter\n             * @see https://tailwindcss.com/docs/backdrop-filter\n             */\n            'backdrop-filter': [\n                {\n                    'backdrop-filter': [\n                        // Deprecated since Tailwind CSS v3.0.0\n                        '',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Backdrop Blur\n             * @see https://tailwindcss.com/docs/backdrop-blur\n             */\n            'backdrop-blur': [{ 'backdrop-blur': scaleBlur() }],\n            /**\n             * Backdrop Brightness\n             * @see https://tailwindcss.com/docs/backdrop-brightness\n             */\n            'backdrop-brightness': [\n                { 'backdrop-brightness': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Contrast\n             * @see https://tailwindcss.com/docs/backdrop-contrast\n             */\n            'backdrop-contrast': [\n                { 'backdrop-contrast': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Grayscale\n             * @see https://tailwindcss.com/docs/backdrop-grayscale\n             */\n            'backdrop-grayscale': [\n                { 'backdrop-grayscale': ['', isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Hue Rotate\n             * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n             */\n            'backdrop-hue-rotate': [\n                { 'backdrop-hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Invert\n             * @see https://tailwindcss.com/docs/backdrop-invert\n             */\n            'backdrop-invert': [\n                { 'backdrop-invert': ['', isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Opacity\n             * @see https://tailwindcss.com/docs/backdrop-opacity\n             */\n            'backdrop-opacity': [\n                { 'backdrop-opacity': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Saturate\n             * @see https://tailwindcss.com/docs/backdrop-saturate\n             */\n            'backdrop-saturate': [\n                { 'backdrop-saturate': [isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Backdrop Sepia\n             * @see https://tailwindcss.com/docs/backdrop-sepia\n             */\n            'backdrop-sepia': [\n                { 'backdrop-sepia': ['', isNumber, isArbitraryVariable, isArbitraryValue] },\n            ],\n\n            // --------------\n            // --- Tables ---\n            // --------------\n\n            /**\n             * Border Collapse\n             * @see https://tailwindcss.com/docs/border-collapse\n             */\n            'border-collapse': [{ border: ['collapse', 'separate'] }],\n            /**\n             * Border Spacing\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing': [{ 'border-spacing': scaleUnambiguousSpacing() }],\n            /**\n             * Border Spacing X\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing-x': [{ 'border-spacing-x': scaleUnambiguousSpacing() }],\n            /**\n             * Border Spacing Y\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing-y': [{ 'border-spacing-y': scaleUnambiguousSpacing() }],\n            /**\n             * Table Layout\n             * @see https://tailwindcss.com/docs/table-layout\n             */\n            'table-layout': [{ table: ['auto', 'fixed'] }],\n            /**\n             * Caption Side\n             * @see https://tailwindcss.com/docs/caption-side\n             */\n            caption: [{ caption: ['top', 'bottom'] }],\n\n            // ---------------------------------\n            // --- Transitions and Animation ---\n            // ---------------------------------\n\n            /**\n             * Transition Property\n             * @see https://tailwindcss.com/docs/transition-property\n             */\n            transition: [\n                {\n                    transition: [\n                        '',\n                        'all',\n                        'colors',\n                        'opacity',\n                        'shadow',\n                        'transform',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Transition Behavior\n             * @see https://tailwindcss.com/docs/transition-behavior\n             */\n            'transition-behavior': [{ transition: ['normal', 'discrete'] }],\n            /**\n             * Transition Duration\n             * @see https://tailwindcss.com/docs/transition-duration\n             */\n            duration: [{ duration: [isNumber, 'initial', isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Transition Timing Function\n             * @see https://tailwindcss.com/docs/transition-timing-function\n             */\n            ease: [\n                { ease: ['linear', 'initial', themeEase, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Transition Delay\n             * @see https://tailwindcss.com/docs/transition-delay\n             */\n            delay: [{ delay: [isNumber, isArbitraryVariable, isArbitraryValue] }],\n            /**\n             * Animation\n             * @see https://tailwindcss.com/docs/animation\n             */\n            animate: [{ animate: ['none', themeAnimate, isArbitraryVariable, isArbitraryValue] }],\n\n            // ------------------\n            // --- Transforms ---\n            // ------------------\n\n            /**\n             * Backface Visibility\n             * @see https://tailwindcss.com/docs/backface-visibility\n             */\n            backface: [{ backface: ['hidden', 'visible'] }],\n            /**\n             * Perspective\n             * @see https://tailwindcss.com/docs/perspective\n             */\n            perspective: [\n                { perspective: [themePerspective, isArbitraryVariable, isArbitraryValue] },\n            ],\n            /**\n             * Perspective Origin\n             * @see https://tailwindcss.com/docs/perspective-origin\n             */\n            'perspective-origin': [{ 'perspective-origin': scalePositionWithArbitrary() }],\n            /**\n             * Rotate\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            rotate: [{ rotate: scaleRotate() }],\n            /**\n             * Rotate X\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            'rotate-x': [{ 'rotate-x': scaleRotate() }],\n            /**\n             * Rotate Y\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            'rotate-y': [{ 'rotate-y': scaleRotate() }],\n            /**\n             * Rotate Z\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            'rotate-z': [{ 'rotate-z': scaleRotate() }],\n            /**\n             * Scale\n             * @see https://tailwindcss.com/docs/scale\n             */\n            scale: [{ scale: scaleScale() }],\n            /**\n             * Scale X\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-x': [{ 'scale-x': scaleScale() }],\n            /**\n             * Scale Y\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-y': [{ 'scale-y': scaleScale() }],\n            /**\n             * Scale Z\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-z': [{ 'scale-z': scaleScale() }],\n            /**\n             * Scale 3D\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-3d': ['scale-3d'],\n            /**\n             * Skew\n             * @see https://tailwindcss.com/docs/skew\n             */\n            skew: [{ skew: scaleSkew() }],\n            /**\n             * Skew X\n             * @see https://tailwindcss.com/docs/skew\n             */\n            'skew-x': [{ 'skew-x': scaleSkew() }],\n            /**\n             * Skew Y\n             * @see https://tailwindcss.com/docs/skew\n             */\n            'skew-y': [{ 'skew-y': scaleSkew() }],\n            /**\n             * Transform\n             * @see https://tailwindcss.com/docs/transform\n             */\n            transform: [\n                { transform: [isArbitraryVariable, isArbitraryValue, '', 'none', 'gpu', 'cpu'] },\n            ],\n            /**\n             * Transform Origin\n             * @see https://tailwindcss.com/docs/transform-origin\n             */\n            'transform-origin': [{ origin: scalePositionWithArbitrary() }],\n            /**\n             * Transform Style\n             * @see https://tailwindcss.com/docs/transform-style\n             */\n            'transform-style': [{ transform: ['3d', 'flat'] }],\n            /**\n             * Translate\n             * @see https://tailwindcss.com/docs/translate\n             */\n            translate: [{ translate: scaleTranslate() }],\n            /**\n             * Translate X\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-x': [{ 'translate-x': scaleTranslate() }],\n            /**\n             * Translate Y\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-y': [{ 'translate-y': scaleTranslate() }],\n            /**\n             * Translate Z\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-z': [{ 'translate-z': scaleTranslate() }],\n            /**\n             * Translate None\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-none': ['translate-none'],\n\n            // ---------------------\n            // --- Interactivity ---\n            // ---------------------\n\n            /**\n             * Accent Color\n             * @see https://tailwindcss.com/docs/accent-color\n             */\n            accent: [{ accent: scaleColor() }],\n            /**\n             * Appearance\n             * @see https://tailwindcss.com/docs/appearance\n             */\n            appearance: [{ appearance: ['none', 'auto'] }],\n            /**\n             * Caret Color\n             * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n             */\n            'caret-color': [{ caret: scaleColor() }],\n            /**\n             * Color Scheme\n             * @see https://tailwindcss.com/docs/color-scheme\n             */\n            'color-scheme': [\n                { scheme: ['normal', 'dark', 'light', 'light-dark', 'only-dark', 'only-light'] },\n            ],\n            /**\n             * Cursor\n             * @see https://tailwindcss.com/docs/cursor\n             */\n            cursor: [\n                {\n                    cursor: [\n                        'auto',\n                        'default',\n                        'pointer',\n                        'wait',\n                        'text',\n                        'move',\n                        'help',\n                        'not-allowed',\n                        'none',\n                        'context-menu',\n                        'progress',\n                        'cell',\n                        'crosshair',\n                        'vertical-text',\n                        'alias',\n                        'copy',\n                        'no-drop',\n                        'grab',\n                        'grabbing',\n                        'all-scroll',\n                        'col-resize',\n                        'row-resize',\n                        'n-resize',\n                        'e-resize',\n                        's-resize',\n                        'w-resize',\n                        'ne-resize',\n                        'nw-resize',\n                        'se-resize',\n                        'sw-resize',\n                        'ew-resize',\n                        'ns-resize',\n                        'nesw-resize',\n                        'nwse-resize',\n                        'zoom-in',\n                        'zoom-out',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Field Sizing\n             * @see https://tailwindcss.com/docs/field-sizing\n             */\n            'field-sizing': [{ 'field-sizing': ['fixed', 'content'] }],\n            /**\n             * Pointer Events\n             * @see https://tailwindcss.com/docs/pointer-events\n             */\n            'pointer-events': [{ 'pointer-events': ['auto', 'none'] }],\n            /**\n             * Resize\n             * @see https://tailwindcss.com/docs/resize\n             */\n            resize: [{ resize: ['none', '', 'y', 'x'] }],\n            /**\n             * Scroll Behavior\n             * @see https://tailwindcss.com/docs/scroll-behavior\n             */\n            'scroll-behavior': [{ scroll: ['auto', 'smooth'] }],\n            /**\n             * Scroll Margin\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-m': [{ 'scroll-m': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin X\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mx': [{ 'scroll-mx': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Y\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-my': [{ 'scroll-my': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Start\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-ms': [{ 'scroll-ms': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin End\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-me': [{ 'scroll-me': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Top\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mt': [{ 'scroll-mt': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Right\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mr': [{ 'scroll-mr': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Bottom\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mb': [{ 'scroll-mb': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Margin Left\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-ml': [{ 'scroll-ml': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-p': [{ 'scroll-p': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding X\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-px': [{ 'scroll-px': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Y\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-py': [{ 'scroll-py': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Start\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-ps': [{ 'scroll-ps': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding End\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pe': [{ 'scroll-pe': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Top\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pt': [{ 'scroll-pt': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Right\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pr': [{ 'scroll-pr': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Bottom\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pb': [{ 'scroll-pb': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Padding Left\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pl': [{ 'scroll-pl': scaleUnambiguousSpacing() }],\n            /**\n             * Scroll Snap Align\n             * @see https://tailwindcss.com/docs/scroll-snap-align\n             */\n            'snap-align': [{ snap: ['start', 'end', 'center', 'align-none'] }],\n            /**\n             * Scroll Snap Stop\n             * @see https://tailwindcss.com/docs/scroll-snap-stop\n             */\n            'snap-stop': [{ snap: ['normal', 'always'] }],\n            /**\n             * Scroll Snap Type\n             * @see https://tailwindcss.com/docs/scroll-snap-type\n             */\n            'snap-type': [{ snap: ['none', 'x', 'y', 'both'] }],\n            /**\n             * Scroll Snap Type Strictness\n             * @see https://tailwindcss.com/docs/scroll-snap-type\n             */\n            'snap-strictness': [{ snap: ['mandatory', 'proximity'] }],\n            /**\n             * Touch Action\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            touch: [{ touch: ['auto', 'none', 'manipulation'] }],\n            /**\n             * Touch Action X\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            'touch-x': [{ 'touch-pan': ['x', 'left', 'right'] }],\n            /**\n             * Touch Action Y\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            'touch-y': [{ 'touch-pan': ['y', 'up', 'down'] }],\n            /**\n             * Touch Action Pinch Zoom\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            'touch-pz': ['touch-pinch-zoom'],\n            /**\n             * User Select\n             * @see https://tailwindcss.com/docs/user-select\n             */\n            select: [{ select: ['none', 'text', 'all', 'auto'] }],\n            /**\n             * Will Change\n             * @see https://tailwindcss.com/docs/will-change\n             */\n            'will-change': [\n                {\n                    'will-change': [\n                        'auto',\n                        'scroll',\n                        'contents',\n                        'transform',\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n\n            // -----------\n            // --- SVG ---\n            // -----------\n\n            /**\n             * Fill\n             * @see https://tailwindcss.com/docs/fill\n             */\n            fill: [{ fill: ['none', ...scaleColor()] }],\n            /**\n             * Stroke Width\n             * @see https://tailwindcss.com/docs/stroke-width\n             */\n            'stroke-w': [\n                {\n                    stroke: [\n                        isNumber,\n                        isArbitraryVariableLength,\n                        isArbitraryLength,\n                        isArbitraryNumber,\n                    ],\n                },\n            ],\n            /**\n             * Stroke\n             * @see https://tailwindcss.com/docs/stroke\n             */\n            stroke: [{ stroke: ['none', ...scaleColor()] }],\n\n            // ---------------------\n            // --- Accessibility ---\n            // ---------------------\n\n            /**\n             * Forced Color Adjust\n             * @see https://tailwindcss.com/docs/forced-color-adjust\n             */\n            'forced-color-adjust': [{ 'forced-color-adjust': ['auto', 'none'] }],\n        },\n        conflictingClassGroups: {\n            overflow: ['overflow-x', 'overflow-y'],\n            overscroll: ['overscroll-x', 'overscroll-y'],\n            inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n            'inset-x': ['right', 'left'],\n            'inset-y': ['top', 'bottom'],\n            flex: ['basis', 'grow', 'shrink'],\n            gap: ['gap-x', 'gap-y'],\n            p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n            px: ['pr', 'pl'],\n            py: ['pt', 'pb'],\n            m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n            mx: ['mr', 'ml'],\n            my: ['mt', 'mb'],\n            size: ['w', 'h'],\n            'font-size': ['leading'],\n            'fvn-normal': [\n                'fvn-ordinal',\n                'fvn-slashed-zero',\n                'fvn-figure',\n                'fvn-spacing',\n                'fvn-fraction',\n            ],\n            'fvn-ordinal': ['fvn-normal'],\n            'fvn-slashed-zero': ['fvn-normal'],\n            'fvn-figure': ['fvn-normal'],\n            'fvn-spacing': ['fvn-normal'],\n            'fvn-fraction': ['fvn-normal'],\n            'line-clamp': ['display', 'overflow'],\n            rounded: [\n                'rounded-s',\n                'rounded-e',\n                'rounded-t',\n                'rounded-r',\n                'rounded-b',\n                'rounded-l',\n                'rounded-ss',\n                'rounded-se',\n                'rounded-ee',\n                'rounded-es',\n                'rounded-tl',\n                'rounded-tr',\n                'rounded-br',\n                'rounded-bl',\n            ],\n            'rounded-s': ['rounded-ss', 'rounded-es'],\n            'rounded-e': ['rounded-se', 'rounded-ee'],\n            'rounded-t': ['rounded-tl', 'rounded-tr'],\n            'rounded-r': ['rounded-tr', 'rounded-br'],\n            'rounded-b': ['rounded-br', 'rounded-bl'],\n            'rounded-l': ['rounded-tl', 'rounded-bl'],\n            'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n            'border-w': [\n                'border-w-x',\n                'border-w-y',\n                'border-w-s',\n                'border-w-e',\n                'border-w-t',\n                'border-w-r',\n                'border-w-b',\n                'border-w-l',\n            ],\n            'border-w-x': ['border-w-r', 'border-w-l'],\n            'border-w-y': ['border-w-t', 'border-w-b'],\n            'border-color': [\n                'border-color-x',\n                'border-color-y',\n                'border-color-s',\n                'border-color-e',\n                'border-color-t',\n                'border-color-r',\n                'border-color-b',\n                'border-color-l',\n            ],\n            'border-color-x': ['border-color-r', 'border-color-l'],\n            'border-color-y': ['border-color-t', 'border-color-b'],\n            translate: ['translate-x', 'translate-y', 'translate-none'],\n            'translate-none': ['translate', 'translate-x', 'translate-y', 'translate-z'],\n            'scroll-m': [\n                'scroll-mx',\n                'scroll-my',\n                'scroll-ms',\n                'scroll-me',\n                'scroll-mt',\n                'scroll-mr',\n                'scroll-mb',\n                'scroll-ml',\n            ],\n            'scroll-mx': ['scroll-mr', 'scroll-ml'],\n            'scroll-my': ['scroll-mt', 'scroll-mb'],\n            'scroll-p': [\n                'scroll-px',\n                'scroll-py',\n                'scroll-ps',\n                'scroll-pe',\n                'scroll-pt',\n                'scroll-pr',\n                'scroll-pb',\n                'scroll-pl',\n            ],\n            'scroll-px': ['scroll-pr', 'scroll-pl'],\n            'scroll-py': ['scroll-pt', 'scroll-pb'],\n            touch: ['touch-x', 'touch-y', 'touch-pz'],\n            'touch-x': ['touch'],\n            'touch-y': ['touch'],\n            'touch-pz': ['touch'],\n        },\n        conflictingClassGroupModifiers: {\n            'font-size': ['leading'],\n        },\n        orderSensitiveModifiers: [\n            '*',\n            '**',\n            'after',\n            'backdrop',\n            'before',\n            'details-content',\n            'file',\n            'first-letter',\n            'first-line',\n            'marker',\n            'placeholder',\n            'selection',\n        ],\n    } as const satisfies Config<DefaultClassGroupIds, DefaultThemeGroupIds>\n}\n", "import { AnyConfig, ConfigExtension, NoInfer } from './types'\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nexport const mergeConfigs = <ClassGroupIds extends string, ThemeGroupIds extends string = never>(\n    baseConfig: AnyConfig,\n    {\n        cacheSize,\n        prefix,\n        experimentalParseClassName,\n        extend = {},\n        override = {},\n    }: ConfigExtension<ClassGroupIds, ThemeGroupIds>,\n) => {\n    overrideProperty(baseConfig, 'cacheSize', cacheSize)\n    overrideProperty(baseConfig, 'prefix', prefix)\n    overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName)\n\n    overrideConfigProperties(baseConfig.theme, override.theme)\n    overrideConfigProperties(baseConfig.classGroups, override.classGroups)\n    overrideConfigProperties(baseConfig.conflictingClassGroups, override.conflictingClassGroups)\n    overrideConfigProperties(\n        baseConfig.conflictingClassGroupModifiers,\n        override.conflictingClassGroupModifiers,\n    )\n    overrideProperty(baseConfig, 'orderSensitiveModifiers', override.orderSensitiveModifiers)\n\n    mergeConfigProperties(baseConfig.theme, extend.theme)\n    mergeConfigProperties(baseConfig.classGroups, extend.classGroups)\n    mergeConfigProperties(baseConfig.conflictingClassGroups, extend.conflictingClassGroups)\n    mergeConfigProperties(\n        baseConfig.conflictingClassGroupModifiers,\n        extend.conflictingClassGroupModifiers,\n    )\n    mergeArrayProperties(baseConfig, extend, 'orderSensitiveModifiers')\n\n    return baseConfig\n}\n\nconst overrideProperty = <T extends object, K extends keyof T>(\n    baseObject: T,\n    overrideKey: K,\n    overrideValue: T[K] | undefined,\n) => {\n    if (overrideValue !== undefined) {\n        baseObject[overrideKey] = overrideValue\n    }\n}\n\nconst overrideConfigProperties = (\n    baseObject: Partial<Record<string, readonly unknown[]>>,\n    overrideObject: Partial<Record<string, readonly unknown[]>> | undefined,\n) => {\n    if (overrideObject) {\n        for (const key in overrideObject) {\n            overrideProperty(baseObject, key, overrideObject[key])\n        }\n    }\n}\n\nconst mergeConfigProperties = (\n    baseObject: Partial<Record<string, readonly unknown[]>>,\n    mergeObject: Partial<Record<string, readonly unknown[]>> | undefined,\n) => {\n    if (mergeObject) {\n        for (const key in mergeObject) {\n            mergeArrayProperties(baseObject, mergeObject, key)\n        }\n    }\n}\n\nconst mergeArrayProperties = <Key extends string>(\n    baseObject: Partial<Record<NoInfer<Key>, readonly unknown[]>>,\n    mergeObject: Partial<Record<NoInfer<Key>, readonly unknown[]>>,\n    key: Key,\n) => {\n    const mergeValue = mergeObject[key]\n\n    if (mergeValue !== undefined) {\n        baseObject[key] = baseObject[key] ? baseObject[key].concat(mergeValue) : mergeValue\n    }\n}\n", "import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\nimport { mergeConfigs } from './merge-configs'\nimport { AnyConfig, ConfigExtension, DefaultClassGroupIds, DefaultThemeGroupIds } from './types'\n\ntype CreateConfigSubsequent = (config: AnyConfig) => AnyConfig\n\nexport const extendTailwindMerge = <\n    AdditionalClassGroupIds extends string = never,\n    AdditionalThemeGroupIds extends string = never,\n>(\n    configExtension:\n        | ConfigExtension<\n              DefaultClassGroupIds | AdditionalClassGroupIds,\n              DefaultThemeGroupIds | AdditionalThemeGroupIds\n          >\n        | CreateConfigSubsequent,\n    ...createConfig: CreateConfigSubsequent[]\n) =>\n    typeof configExtension === 'function'\n        ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig)\n        : createTailwindMerge(\n              () => mergeConfigs(getDefaultConfig(), configExtension),\n              ...createConfig,\n          )\n", "import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\n\nexport const twMerge = createTailwindMerge(getDefaultConfig)\n"], "mappings": ";;;AAsBA,IAAMA,uBAAuB;AAEtB,IAAMC,wBAAyBC,YAAqB;AACvD,QAAMC,WAAWC,eAAeF,MAAM;AACtC,QAAM;IAAEG;IAAwBC;EAA8B,IAAKJ;AAEnE,QAAMK,kBAAmBC,eAAqB;AAC1C,UAAMC,aAAaD,UAAUE,MAAMV,oBAAoB;AAGvD,QAAIS,WAAW,CAAC,MAAM,MAAMA,WAAWE,WAAW,GAAG;AACjDF,iBAAWG,MAAO;;AAGtB,WAAOC,kBAAkBJ,YAAYN,QAAQ,KAAKW,+BAA+BN,SAAS;EAC7F;AAED,QAAMO,8BAA8BA,CAChCC,cACAC,uBACA;AACA,UAAMC,YAAYb,uBAAuBW,YAAY,KAAK,CAAA;AAE1D,QAAIC,sBAAsBX,+BAA+BU,YAAY,GAAG;AACpE,aAAO,CAAC,GAAGE,WAAW,GAAGZ,+BAA+BU,YAAY,CAAE;;AAG1E,WAAOE;EACV;AAED,SAAO;IACHX;IACAQ;EACH;AACL;AAEA,IAAMF,oBAAoBA,CACtBJ,YACAU,oBAC8B;AAC9B,MAAIV,WAAWE,WAAW,GAAG;AACzB,WAAOQ,gBAAgBH;;AAG3B,QAAMI,mBAAmBX,WAAW,CAAC;AACrC,QAAMY,sBAAsBF,gBAAgBG,SAASC,IAAIH,gBAAgB;AACzE,QAAMI,8BAA8BH,sBAC9BR,kBAAkBJ,WAAWgB,MAAM,CAAC,GAAGJ,mBAAmB,IAC1DK;AAEN,MAAIF,6BAA6B;AAC7B,WAAOA;;AAGX,MAAIL,gBAAgBQ,WAAWhB,WAAW,GAAG;AACzC,WAAOe;;AAGX,QAAME,YAAYnB,WAAWoB,KAAK7B,oBAAoB;AAEtD,SAAOmB,gBAAgBQ,WAAWG,KAAK,CAAC;IAAEC;EAAS,MAAOA,UAAUH,SAAS,CAAC,GAAGZ;AACrF;AAEA,IAAMgB,yBAAyB;AAE/B,IAAMlB,iCAAkCN,eAAqB;AACzD,MAAIwB,uBAAuBC,KAAKzB,SAAS,GAAG;AACxC,UAAM0B,6BAA6BF,uBAAuBG,KAAK3B,SAAS,EAAG,CAAC;AAC5E,UAAM4B,WAAWF,4BAA4BG,UACzC,GACAH,2BAA2BI,QAAQ,GAAG,CAAC;AAG3C,QAAIF,UAAU;AAEV,aAAO,gBAAgBA;;;AAGnC;AAKO,IAAMhC,iBAAkBF,YAAsD;AACjF,QAAM;IAAEqC;IAAOC;EAAW,IAAKtC;AAC/B,QAAMC,WAA4B;IAC9BmB,UAAU,oBAAImB,IAA8B;IAC5Cd,YAAY,CAAA;EACf;AAED,aAAWX,gBAAgBwB,aAAa;AACpCE,8BAA0BF,YAAYxB,YAAY,GAAIb,UAAUa,cAAcuB,KAAK;;AAGvF,SAAOpC;AACX;AAEA,IAAMuC,4BAA4BA,CAC9BC,YACAxB,iBACAH,cACAuB,UACA;AACAI,aAAWC,QAASC,qBAAmB;AACnC,QAAI,OAAOA,oBAAoB,UAAU;AACrC,YAAMC,wBACFD,oBAAoB,KAAK1B,kBAAkB4B,QAAQ5B,iBAAiB0B,eAAe;AACvFC,4BAAsB9B,eAAeA;AACrC;;AAGJ,QAAI,OAAO6B,oBAAoB,YAAY;AACvC,UAAIG,cAAcH,eAAe,GAAG;AAChCH,kCACIG,gBAAgBN,KAAK,GACrBpB,iBACAH,cACAuB,KAAK;AAET;;AAGJpB,sBAAgBQ,WAAWsB,KAAK;QAC5BlB,WAAWc;QACX7B;MACH,CAAA;AAED;;AAGJkC,WAAOC,QAAQN,eAAe,EAAED,QAAQ,CAAC,CAACQ,KAAKT,WAAU,MAAK;AAC1DD,gCACIC,aACAI,QAAQ5B,iBAAiBiC,GAAG,GAC5BpC,cACAuB,KAAK;IAEb,CAAC;EACL,CAAC;AACL;AAEA,IAAMQ,UAAUA,CAAC5B,iBAAkCkC,SAAgB;AAC/D,MAAIC,yBAAyBnC;AAE7BkC,OAAK3C,MAAMV,oBAAoB,EAAE4C,QAASW,cAAY;AAClD,QAAI,CAACD,uBAAuBhC,SAASkC,IAAID,QAAQ,GAAG;AAChDD,6BAAuBhC,SAASmC,IAAIF,UAAU;QAC1CjC,UAAU,oBAAImB,IAAK;QACnBd,YAAY,CAAA;MACf,CAAA;;AAGL2B,6BAAyBA,uBAAuBhC,SAASC,IAAIgC,QAAQ;EACzE,CAAC;AAED,SAAOD;AACX;AAEA,IAAMN,gBAAiBU,UAClBA,KAAqBV;AC7KnB,IAAMW,iBAA8BC,kBAA8C;AACrF,MAAIA,eAAe,GAAG;AAClB,WAAO;MACHrC,KAAKA,MAAMG;MACX+B,KAAKA,MAAK;MAAG;IAChB;;AAGL,MAAII,YAAY;AAChB,MAAIC,QAAQ,oBAAIrB,IAAiB;AACjC,MAAIsB,gBAAgB,oBAAItB,IAAiB;AAEzC,QAAMuB,SAASA,CAACZ,KAAUa,UAAgB;AACtCH,UAAML,IAAIL,KAAKa,KAAK;AACpBJ;AAEA,QAAIA,YAAYD,cAAc;AAC1BC,kBAAY;AACZE,sBAAgBD;AAChBA,cAAQ,oBAAIrB,IAAK;;EAExB;AAED,SAAO;IACHlB,IAAI6B,KAAG;AACH,UAAIa,QAAQH,MAAMvC,IAAI6B,GAAG;AAEzB,UAAIa,UAAUvC,QAAW;AACrB,eAAOuC;;AAEX,WAAKA,QAAQF,cAAcxC,IAAI6B,GAAG,OAAO1B,QAAW;AAChDsC,eAAOZ,KAAKa,KAAK;AACjB,eAAOA;;IAEd;IACDR,IAAIL,KAAKa,OAAK;AACV,UAAIH,MAAMN,IAAIJ,GAAG,GAAG;AAChBU,cAAML,IAAIL,KAAKa,KAAK;aACjB;AACHD,eAAOZ,KAAKa,KAAK;;IAExB;EACJ;AACL;ACjDO,IAAMC,qBAAqB;AAClC,IAAMC,qBAAqB;AAC3B,IAAMC,4BAA4BD,mBAAmBxD;AAE9C,IAAM0D,uBAAwBnE,YAAqB;AACtD,QAAM;IAAEoE;IAAQC;EAA0B,IAAKrE;AAQ/C,MAAIsE,iBAAkBhE,eAAsC;AACxD,UAAMiE,YAAY,CAAA;AAElB,QAAIC,eAAe;AACnB,QAAIC,aAAa;AACjB,QAAIC,gBAAgB;AACpB,QAAIC;AAEJ,aAASC,QAAQ,GAAGA,QAAQtE,UAAUG,QAAQmE,SAAS;AACnD,UAAIC,mBAAmBvE,UAAUsE,KAAK;AAEtC,UAAIJ,iBAAiB,KAAKC,eAAe,GAAG;AACxC,YAAII,qBAAqBZ,oBAAoB;AACzCM,oBAAUxB,KAAKzC,UAAUiB,MAAMmD,eAAeE,KAAK,CAAC;AACpDF,0BAAgBE,QAAQV;AACxB;;AAGJ,YAAIW,qBAAqB,KAAK;AAC1BF,oCAA0BC;AAC1B;;;AAIR,UAAIC,qBAAqB,KAAK;AAC1BL;iBACOK,qBAAqB,KAAK;AACjCL;iBACOK,qBAAqB,KAAK;AACjCJ;iBACOI,qBAAqB,KAAK;AACjCJ;;;AAIR,UAAMK,qCACFP,UAAU9D,WAAW,IAAIH,YAAYA,UAAU6B,UAAUuC,aAAa;AAC1E,UAAMK,gBAAgBC,uBAAuBF,kCAAkC;AAC/E,UAAMG,uBAAuBF,kBAAkBD;AAC/C,UAAMI,+BACFP,2BAA2BA,0BAA0BD,gBAC/CC,0BAA0BD,gBAC1BlD;AAEV,WAAO;MACH+C;MACAU;MACAF;MACAG;IACH;EACJ;AAED,MAAId,QAAQ;AACR,UAAMe,aAAaf,SAASH;AAC5B,UAAMmB,yBAAyBd;AAC/BA,qBAAkBhE,eACdA,UAAU+E,WAAWF,UAAU,IACzBC,uBAAuB9E,UAAU6B,UAAUgD,WAAW1E,MAAM,CAAC,IAC7D;MACI6E,YAAY;MACZf,WAAW,CAAA;MACXU,sBAAsB;MACtBF,eAAezE;MACf4E,8BAA8B1D;IACjC;;AAGf,MAAI6C,4BAA4B;AAC5B,UAAMe,yBAAyBd;AAC/BA,qBAAkBhE,eACd+D,2BAA2B;MAAE/D;MAAWgE,gBAAgBc;KAAwB;;AAGxF,SAAOd;AACX;AAEA,IAAMU,yBAA0BD,mBAAyB;AACrD,MAAIA,cAAcQ,SAASvB,kBAAkB,GAAG;AAC5C,WAAOe,cAAc5C,UAAU,GAAG4C,cAActE,SAAS,CAAC;;AAO9D,MAAIsE,cAAcM,WAAWrB,kBAAkB,GAAG;AAC9C,WAAOe,cAAc5C,UAAU,CAAC;;AAGpC,SAAO4C;AACX;AClGO,IAAMS,sBAAuBxF,YAAqB;AACrD,QAAMyF,0BAA0BzC,OAAO0C,YACnC1F,OAAOyF,wBAAwBE,IAAKC,cAAa,CAACA,UAAU,IAAI,CAAC,CAAC;AAGtE,QAAMC,gBAAiBtB,eAAuB;AAC1C,QAAIA,UAAU9D,UAAU,GAAG;AACvB,aAAO8D;;AAGX,UAAMuB,kBAA4B,CAAA;AAClC,QAAIC,oBAA8B,CAAA;AAElCxB,cAAU7B,QAASkD,cAAY;AAC3B,YAAMI,sBAAsBJ,SAAS,CAAC,MAAM,OAAOH,wBAAwBG,QAAQ;AAEnF,UAAII,qBAAqB;AACrBF,wBAAgB/C,KAAK,GAAGgD,kBAAkBE,KAAI,GAAIL,QAAQ;AAC1DG,4BAAoB,CAAA;aACjB;AACHA,0BAAkBhD,KAAK6C,QAAQ;;IAEvC,CAAC;AAEDE,oBAAgB/C,KAAK,GAAGgD,kBAAkBE,KAAI,CAAE;AAEhD,WAAOH;EACV;AAED,SAAOD;AACX;AC7BO,IAAMK,oBAAqBlG,aAAuB;EACrD4D,OAAOH,eAA+BzD,OAAO2D,SAAS;EACtDW,gBAAgBH,qBAAqBnE,MAAM;EAC3C6F,eAAeL,oBAAoBxF,MAAM;EACzC,GAAGD,sBAAsBC,MAAM;AAClC;ACVD,IAAMmG,sBAAsB;AAErB,IAAMC,iBAAiBA,CAACC,WAAmBC,gBAA4B;AAC1E,QAAM;IAAEhC;IAAgBjE;IAAiBQ;IAA6BgF;EAAe,IACjFS;AASJ,QAAMC,wBAAkC,CAAA;AACxC,QAAMC,aAAaH,UAAUI,KAAI,EAAGjG,MAAM2F,mBAAmB;AAE7D,MAAIO,SAAS;AAEb,WAAS9B,QAAQ4B,WAAW/F,SAAS,GAAGmE,SAAS,GAAGA,SAAS,GAAG;AAC5D,UAAM+B,oBAAoBH,WAAW5B,KAAK;AAE1C,UAAM;MACFU;MACAf;MACAU;MACAF;MACAG;IACH,IAAGZ,eAAeqC,iBAAiB;AAEpC,QAAIrB,YAAY;AACZoB,eAASC,qBAAqBD,OAAOjG,SAAS,IAAI,MAAMiG,SAASA;AACjE;;AAGJ,QAAI3F,qBAAqB,CAAC,CAACmE;AAC3B,QAAIpE,eAAeT,gBACfU,qBACMgE,cAAc5C,UAAU,GAAG+C,4BAA4B,IACvDH,aAAa;AAGvB,QAAI,CAACjE,cAAc;AACf,UAAI,CAACC,oBAAoB;AAErB2F,iBAASC,qBAAqBD,OAAOjG,SAAS,IAAI,MAAMiG,SAASA;AACjE;;AAGJ5F,qBAAeT,gBAAgB0E,aAAa;AAE5C,UAAI,CAACjE,cAAc;AAEf4F,iBAASC,qBAAqBD,OAAOjG,SAAS,IAAI,MAAMiG,SAASA;AACjE;;AAGJ3F,2BAAqB;;AAGzB,UAAM6F,kBAAkBf,cAActB,SAAS,EAAE5C,KAAK,GAAG;AAEzD,UAAMkF,aAAa5B,uBACb2B,kBAAkB5C,qBAClB4C;AAEN,UAAME,UAAUD,aAAa/F;AAE7B,QAAIyF,sBAAsBQ,SAASD,OAAO,GAAG;AAEzC;;AAGJP,0BAAsBxD,KAAK+D,OAAO;AAElC,UAAME,iBAAiBnG,4BAA4BC,cAAcC,kBAAkB;AACnF,aAASkG,IAAI,GAAGA,IAAID,eAAevG,QAAQ,EAAEwG,GAAG;AAC5C,YAAMC,QAAQF,eAAeC,CAAC;AAC9BV,4BAAsBxD,KAAK8D,aAAaK,KAAK;;AAIjDR,aAASC,qBAAqBD,OAAOjG,SAAS,IAAI,MAAMiG,SAASA;;AAGrE,SAAOA;AACX;SC1EgBS,SAAM;AAClB,MAAIvC,QAAQ;AACZ,MAAIwC;AACJ,MAAIC;AACJ,MAAIC,SAAS;AAEb,SAAO1C,QAAQ2C,UAAU9G,QAAQ;AAC7B,QAAK2G,WAAWG,UAAU3C,OAAO,GAAI;AACjC,UAAKyC,gBAAgBG,QAAQJ,QAAQ,GAAI;AACrCE,mBAAWA,UAAU;AACrBA,kBAAUD;;;;AAItB,SAAOC;AACX;AAEA,IAAME,UAAWC,SAAgC;AAC7C,MAAI,OAAOA,QAAQ,UAAU;AACzB,WAAOA;;AAGX,MAAIJ;AACJ,MAAIC,SAAS;AAEb,WAASI,IAAI,GAAGA,IAAID,IAAIhH,QAAQiH,KAAK;AACjC,QAAID,IAAIC,CAAC,GAAG;AACR,UAAKL,gBAAgBG,QAAQC,IAAIC,CAAC,CAA4B,GAAI;AAC9DJ,mBAAWA,UAAU;AACrBA,kBAAUD;;;;AAKtB,SAAOC;AACX;SCvCgBK,oBACZC,sBACGC,kBAA0C;AAE7C,MAAIvB;AACJ,MAAIwB;AACJ,MAAIC;AACJ,MAAIC,iBAAiBC;AAErB,WAASA,kBAAkB5B,WAAiB;AACxC,UAAMrG,SAAS6H,iBAAiBK,OAC5B,CAACC,gBAAgBC,wBAAwBA,oBAAoBD,cAAc,GAC3EP,kBAAiB,CAAe;AAGpCtB,kBAAcJ,kBAAkBlG,MAAM;AACtC8H,eAAWxB,YAAY1C,MAAMvC;AAC7B0G,eAAWzB,YAAY1C,MAAML;AAC7ByE,qBAAiBK;AAEjB,WAAOA,cAAchC,SAAS;;AAGlC,WAASgC,cAAchC,WAAiB;AACpC,UAAMiC,eAAeR,SAASzB,SAAS;AAEvC,QAAIiC,cAAc;AACd,aAAOA;;AAGX,UAAM5B,SAASN,eAAeC,WAAWC,WAAW;AACpDyB,aAAS1B,WAAWK,MAAM;AAE1B,WAAOA;;AAGX,SAAO,SAAS6B,oBAAiB;AAC7B,WAAOP,eAAeb,OAAOqB,MAAM,MAAMjB,SAAgB,CAAC;EAC7D;AACL;AC/Ca,IAAAkB,YAGXvF,SAAkF;AAChF,QAAMwF,cAAerG,WACjBA,MAAMa,GAAG,KAAK,CAAA;AAElBwF,cAAY5F,gBAAgB;AAE5B,SAAO4F;AACX;ACZA,IAAMC,sBAAsB;AAC5B,IAAMC,yBAAyB;AAC/B,IAAMC,gBAAgB;AACtB,IAAMC,kBAAkB;AACxB,IAAMC,kBACF;AACJ,IAAMC,qBAAqB;AAE3B,IAAMC,cAAc;AACpB,IAAMC,aACF;AAEG,IAAMC,aAAcpF,WAAkB8E,cAAc9G,KAAKgC,KAAK;AAE9D,IAAMqF,WAAYrF,WAAkB,CAAC,CAACA,SAAS,CAACsF,OAAOC,MAAMD,OAAOtF,KAAK,CAAC;AAE1E,IAAMwF,YAAaxF,WAAkB,CAAC,CAACA,SAASsF,OAAOE,UAAUF,OAAOtF,KAAK,CAAC;AAE9E,IAAMyF,YAAazF,WAAkBA,MAAMwB,SAAS,GAAG,KAAK6D,SAASrF,MAAMxC,MAAM,GAAG,EAAE,CAAC;AAEvF,IAAMkI,eAAgB1F,WAAkB+E,gBAAgB/G,KAAKgC,KAAK;AAElE,IAAM2F,QAAQA,MAAM;AAE3B,IAAMC,eAAgB5F;;;;EAIlBgF,gBAAgBhH,KAAKgC,KAAK,KAAK,CAACiF,mBAAmBjH,KAAKgC,KAAK;;AAEjE,IAAM6F,UAAUA,MAAM;AAEtB,IAAMC,WAAY9F,WAAkBkF,YAAYlH,KAAKgC,KAAK;AAE1D,IAAM+F,UAAW/F,WAAkBmF,WAAWnH,KAAKgC,KAAK;AAEjD,IAAMgG,oBAAqBhG,WAC9B,CAACiG,iBAAiBjG,KAAK,KAAK,CAACkG,oBAAoBlG,KAAK;AAEnD,IAAMmG,kBAAmBnG,WAAkBoG,oBAAoBpG,OAAOqG,aAAaR,OAAO;AAE1F,IAAMI,mBAAoBjG,WAAkB4E,oBAAoB5G,KAAKgC,KAAK;AAE1E,IAAMsG,oBAAqBtG,WAC9BoG,oBAAoBpG,OAAOuG,eAAeX,YAAY;AAEnD,IAAMY,oBAAqBxG,WAC9BoG,oBAAoBpG,OAAOyG,eAAepB,QAAQ;AAE/C,IAAMqB,sBAAuB1G,WAChCoG,oBAAoBpG,OAAO2G,iBAAiBd,OAAO;AAEhD,IAAMe,mBAAoB5G,WAAkBoG,oBAAoBpG,OAAO6G,cAAcd,OAAO;AAE5F,IAAMe,oBAAqB9G,WAC9BoG,oBAAoBpG,OAAO+G,eAAejB,QAAQ;AAE/C,IAAMI,sBAAuBlG,WAAkB6E,uBAAuB7G,KAAKgC,KAAK;AAEhF,IAAMgH,4BAA6BhH,WACtCiH,uBAAuBjH,OAAOuG,aAAa;AAExC,IAAMW,gCAAiClH,WAC1CiH,uBAAuBjH,OAAOmH,iBAAiB;AAE5C,IAAMC,8BAA+BpH,WACxCiH,uBAAuBjH,OAAO2G,eAAe;AAE1C,IAAMU,0BAA2BrH,WAAkBiH,uBAAuBjH,OAAOqG,WAAW;AAE5F,IAAMiB,2BAA4BtH,WACrCiH,uBAAuBjH,OAAO6G,YAAY;AAEvC,IAAMU,4BAA6BvH,WACtCiH,uBAAuBjH,OAAO+G,eAAe,IAAI;AAIrD,IAAMX,sBAAsBA,CACxBpG,OACAwH,WACAC,cACA;AACA,QAAM9E,SAASiC,oBAAoB1G,KAAK8B,KAAK;AAE7C,MAAI2C,QAAQ;AACR,QAAIA,OAAO,CAAC,GAAG;AACX,aAAO6E,UAAU7E,OAAO,CAAC,CAAC;;AAG9B,WAAO8E,UAAU9E,OAAO,CAAC,CAAE;;AAG/B,SAAO;AACX;AAEA,IAAMsE,yBAAyBA,CAC3BjH,OACAwH,WACAE,qBAAqB,UACrB;AACA,QAAM/E,SAASkC,uBAAuB3G,KAAK8B,KAAK;AAEhD,MAAI2C,QAAQ;AACR,QAAIA,OAAO,CAAC,GAAG;AACX,aAAO6E,UAAU7E,OAAO,CAAC,CAAC;;AAE9B,WAAO+E;;AAGX,SAAO;AACX;AAIA,IAAMf,kBAAmBgB,WAAkBA,UAAU,cAAcA,UAAU;AAE7E,IAAMd,eAAgBc,WAAkBA,UAAU,WAAWA,UAAU;AAEvE,IAAMtB,cAAesB,WAAkBA,UAAU,YAAYA,UAAU,UAAUA,UAAU;AAE3F,IAAMpB,gBAAiBoB,WAAkBA,UAAU;AAEnD,IAAMlB,gBAAiBkB,WAAkBA,UAAU;AAEnD,IAAMR,oBAAqBQ,WAAkBA,UAAU;AAEvD,IAAMZ,gBAAiBY,WAAkBA,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrG5C,IAAMC,mBAAmBA,MAAK;AAOjC,QAAMC,aAAanD,UAAU,OAAO;AACpC,QAAMoD,YAAYpD,UAAU,MAAM;AAClC,QAAMqD,YAAYrD,UAAU,MAAM;AAClC,QAAMsD,kBAAkBtD,UAAU,aAAa;AAC/C,QAAMuD,gBAAgBvD,UAAU,UAAU;AAC1C,QAAMwD,eAAexD,UAAU,SAAS;AACxC,QAAMyD,kBAAkBzD,UAAU,YAAY;AAC9C,QAAM0D,iBAAiB1D,UAAU,WAAW;AAC5C,QAAM2D,eAAe3D,UAAU,SAAS;AACxC,QAAM4D,cAAc5D,UAAU,QAAQ;AACtC,QAAM6D,cAAc7D,UAAU,QAAQ;AACtC,QAAM8D,mBAAmB9D,UAAU,cAAc;AACjD,QAAM+D,kBAAkB/D,UAAU,aAAa;AAC/C,QAAMgE,kBAAkBhE,UAAU,aAAa;AAC/C,QAAMiE,YAAYjE,UAAU,MAAM;AAClC,QAAMkE,mBAAmBlE,UAAU,aAAa;AAChD,QAAMmE,cAAcnE,UAAU,QAAQ;AACtC,QAAMoE,YAAYpE,UAAU,MAAM;AAClC,QAAMqE,eAAerE,UAAU,SAAS;AAUxC,QAAMsE,aAAaA,MACf,CAAC,QAAQ,SAAS,OAAO,cAAc,QAAQ,QAAQ,SAAS,QAAQ;AAC5E,QAAMC,gBAAgBA,MAClB;IACI;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;;IAEA;IACA;;IAEA;IACA;;IAEA;EAAa;AAErB,QAAMC,6BAA6BA,MAC/B,CAAC,GAAGD,cAAa,GAAI/C,qBAAqBD,gBAAgB;AAC9D,QAAMkD,gBAAgBA,MAAM,CAAC,QAAQ,UAAU,QAAQ,WAAW,QAAQ;AAC1E,QAAMC,kBAAkBA,MAAM,CAAC,QAAQ,WAAW,MAAM;AACxD,QAAMC,0BAA0BA,MAC5B,CAACnD,qBAAqBD,kBAAkBoC,YAAY;AACxD,QAAMiB,aAAaA,MAAM,CAAClE,YAAY,QAAQ,QAAQ,GAAGiE,wBAAuB,CAAE;AAClF,QAAME,4BAA4BA,MAC9B,CAAC/D,WAAW,QAAQ,WAAWU,qBAAqBD,gBAAgB;AACxE,QAAMuD,6BAA6BA,MAC/B,CACI,QACA;IAAEC,MAAM,CAAC,QAAQjE,WAAWU,qBAAqBD,gBAAgB;EAAG,GACpET,WACAU,qBACAD,gBAAgB;AAExB,QAAMyD,4BAA4BA,MAC9B,CAAClE,WAAW,QAAQU,qBAAqBD,gBAAgB;AAC7D,QAAM0D,wBAAwBA,MAC1B,CAAC,QAAQ,OAAO,OAAO,MAAMzD,qBAAqBD,gBAAgB;AACtE,QAAM2D,wBAAwBA,MAC1B,CACI,SACA,OACA,UACA,WACA,UACA,UACA,WACA,YACA,eACA,UAAU;AAElB,QAAMC,0BAA0BA,MAC5B,CAAC,SAAS,OAAO,UAAU,WAAW,eAAe,UAAU;AACnE,QAAMC,cAAcA,MAAM,CAAC,QAAQ,GAAGT,wBAAuB,CAAE;AAC/D,QAAMU,cAAcA,MAChB,CACI3E,YACA,QACA,QACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,GAAGiE,wBAAyB,CAAA;AAEpC,QAAMW,aAAaA,MAAM,CAACnC,YAAY3B,qBAAqBD,gBAAgB;AAC3E,QAAMgE,kBAAkBA,MACpB,CACI,GAAGhB,cAAe,GAClB7B,6BACAV,qBACA;IAAEwD,UAAU,CAAChE,qBAAqBD,gBAAgB;EAAG,CAAA;AAE7D,QAAMkE,gBAAgBA,MAAM,CAAC,aAAa;IAAEC,QAAQ,CAAC,IAAI,KAAK,KAAK,SAAS,OAAO;EAAC,CAAE;AACtF,QAAMC,cAAcA,MAChB,CACI,QACA,SACA,WACAhD,yBACAlB,iBACA;IAAEmE,MAAM,CAACpE,qBAAqBD,gBAAgB;EAAG,CAAA;AAEzD,QAAMsE,4BAA4BA,MAC9B,CAAC9E,WAAWuB,2BAA2BV,iBAAiB;AAC5D,QAAMkE,cAAcA,MAChB;;IAEI;IACA;IACA;IACAlC;IACApC;IACAD;EAAgB;AAExB,QAAMwE,mBAAmBA,MACrB,CAAC,IAAIpF,UAAU2B,2BAA2BV,iBAAiB;AAC/D,QAAMoE,iBAAiBA,MAAM,CAAC,SAAS,UAAU,UAAU,QAAQ;AACnE,QAAMC,iBAAiBA,MACnB,CACI,UACA,YACA,UACA,WACA,UACA,WACA,eACA,cACA,cACA,cACA,cACA,aACA,OACA,cACA,SACA,YAAY;AAEpB,QAAMC,yBAAyBA,MAC3B,CAACvF,UAAUI,WAAW2B,6BAA6BV,mBAAmB;AAC1E,QAAMmE,YAAYA,MACd;;IAEI;IACA;IACAlC;IACAzC;IACAD;EAAgB;AAExB,QAAM6E,cAAcA,MAAM,CAAC,QAAQzF,UAAUa,qBAAqBD,gBAAgB;AAClF,QAAM8E,aAAaA,MAAM,CAAC,QAAQ1F,UAAUa,qBAAqBD,gBAAgB;AACjF,QAAM+E,YAAYA,MAAM,CAAC3F,UAAUa,qBAAqBD,gBAAgB;AACxE,QAAMgF,iBAAiBA,MAAM,CAAC7F,YAAY,QAAQ,GAAGiE,wBAAuB,CAAE;AAE9E,SAAO;IACHzJ,WAAW;IACXtB,OAAO;MACH4M,SAAS,CAAC,QAAQ,QAAQ,SAAS,QAAQ;MAC3CC,QAAQ,CAAC,OAAO;MAChBC,MAAM,CAAC1F,YAAY;MACnB2F,YAAY,CAAC3F,YAAY;MACzB4F,OAAO,CAAC3F,KAAK;MACb4F,WAAW,CAAC7F,YAAY;MACxB,eAAe,CAACA,YAAY;MAC5B8F,MAAM,CAAC,MAAM,OAAO,QAAQ;MAC5BC,MAAM,CAACzF,iBAAiB;MACxB,eAAe,CACX,QACA,cACA,SACA,UACA,UACA,YACA,QACA,aACA,OAAO;MAEX,gBAAgB,CAACN,YAAY;MAC7BgG,SAAS,CAAC,QAAQ,SAAS,QAAQ,UAAU,WAAW,OAAO;MAC/DC,aAAa,CAAC,YAAY,QAAQ,UAAU,YAAY,WAAW,MAAM;MACzEC,QAAQ,CAAClG,YAAY;MACrBmG,QAAQ,CAACnG,YAAY;MACrBoG,SAAS,CAAC,MAAMzG,QAAQ;MACxB0G,MAAM,CAACrG,YAAY;MACnB,eAAe,CAACA,YAAY;MAC5BsG,UAAU,CAAC,WAAW,SAAS,UAAU,QAAQ,SAAS,QAAQ;IACrE;IACDzN,aAAa;;;;;;;;MAST4M,QAAQ,CACJ;QACIA,QAAQ,CACJ,QACA,UACA/F,YACAa,kBACAC,qBACA2C,WAAW;MAElB,CAAA;;;;;;MAOL0C,WAAW,CAAC,WAAW;;;;;MAKvBU,SAAS,CACL;QAAEA,SAAS,CAAC5G,UAAUY,kBAAkBC,qBAAqBkC,cAAc;MAAG,CAAA;;;;;MAMlF,eAAe,CAAC;QAAE,eAAeY,WAAY;MAAA,CAAE;;;;;MAK/C,gBAAgB,CAAC;QAAE,gBAAgBA,WAAY;MAAA,CAAE;;;;;MAKjD,gBAAgB,CAAC;QAAE,gBAAgB,CAAC,QAAQ,SAAS,cAAc,cAAc;OAAG;;;;;MAKpF,kBAAkB,CAAC;QAAE,kBAAkB,CAAC,SAAS,OAAO;MAAC,CAAE;;;;;MAK3DkD,KAAK,CAAC;QAAEA,KAAK,CAAC,UAAU,SAAS;MAAC,CAAE;;;;;MAKpCC,SAAS,CACL,SACA,gBACA,UACA,QACA,eACA,SACA,gBACA,iBACA,cACA,gBACA,sBACA,sBACA,sBACA,mBACA,aACA,aACA,QACA,eACA,YACA,aACA,QAAQ;;;;;MAMZC,IAAI,CAAC,WAAW,aAAa;;;;;MAK7BC,OAAO,CAAC;QAAEA,OAAO,CAAC,SAAS,QAAQ,QAAQ,SAAS,KAAK;OAAG;;;;;MAK5DC,OAAO,CAAC;QAAEA,OAAO,CAAC,QAAQ,SAAS,QAAQ,QAAQ,SAAS,KAAK;OAAG;;;;;MAKpEC,WAAW,CAAC,WAAW,gBAAgB;;;;;MAKvC,cAAc,CAAC;QAAEC,QAAQ,CAAC,WAAW,SAAS,QAAQ,QAAQ,YAAY;OAAG;;;;;MAK7E,mBAAmB,CAAC;QAAEA,QAAQtD,2BAA4B;MAAA,CAAE;;;;;MAK5DuD,UAAU,CAAC;QAAEA,UAAUtD,cAAe;MAAA,CAAE;;;;;MAKxC,cAAc,CAAC;QAAE,cAAcA,cAAe;MAAA,CAAE;;;;;MAKhD,cAAc,CAAC;QAAE,cAAcA,cAAe;MAAA,CAAE;;;;;MAKhDuD,YAAY,CAAC;QAAEA,YAAYtD,gBAAiB;MAAA,CAAE;;;;;MAK9C,gBAAgB,CAAC;QAAE,gBAAgBA,gBAAiB;MAAA,CAAE;;;;;MAKtD,gBAAgB,CAAC;QAAE,gBAAgBA,gBAAiB;MAAA,CAAE;;;;;MAKtDc,UAAU,CAAC,UAAU,SAAS,YAAY,YAAY,QAAQ;;;;;MAK9DyC,OAAO,CAAC;QAAEA,OAAOrD,WAAY;MAAA,CAAE;;;;;MAK/B,WAAW,CAAC;QAAE,WAAWA,WAAY;MAAA,CAAE;;;;;MAKvC,WAAW,CAAC;QAAE,WAAWA,WAAY;MAAA,CAAE;;;;;MAKvCsD,OAAO,CAAC;QAAEA,OAAOtD,WAAY;MAAA,CAAE;;;;;MAK/BuD,KAAK,CAAC;QAAEA,KAAKvD,WAAY;MAAA,CAAE;;;;;MAK3BwD,KAAK,CAAC;QAAEA,KAAKxD,WAAY;MAAA,CAAE;;;;;MAK3ByD,OAAO,CAAC;QAAEA,OAAOzD,WAAY;MAAA,CAAE;;;;;MAK/B0D,QAAQ,CAAC;QAAEA,QAAQ1D,WAAY;MAAA,CAAE;;;;;MAKjC2D,MAAM,CAAC;QAAEA,MAAM3D,WAAY;MAAA,CAAE;;;;;MAK7B4D,YAAY,CAAC,WAAW,aAAa,UAAU;;;;;MAK/CC,GAAG,CAAC;QAAEA,GAAG,CAAC3H,WAAW,QAAQU,qBAAqBD,gBAAgB;OAAG;;;;;;;;MAUrEmH,OAAO,CACH;QACIA,OAAO,CACHhI,YACA,QACA,QACAgD,gBACA,GAAGiB,wBAAyB,CAAA;MAEnC,CAAA;;;;;MAML,kBAAkB,CAAC;QAAEgE,MAAM,CAAC,OAAO,eAAe,OAAO,aAAa;OAAG;;;;;MAKzE,aAAa,CAAC;QAAEA,MAAM,CAAC,UAAU,QAAQ,cAAc;OAAG;;;;;MAK1DA,MAAM,CAAC;QAAEA,MAAM,CAAChI,UAAUD,YAAY,QAAQ,WAAW,QAAQa,gBAAgB;OAAG;;;;;MAKpFqH,MAAM,CAAC;QAAEA,MAAM,CAAC,IAAIjI,UAAUa,qBAAqBD,gBAAgB;OAAG;;;;;MAKtEsH,QAAQ,CAAC;QAAEA,QAAQ,CAAC,IAAIlI,UAAUa,qBAAqBD,gBAAgB;OAAG;;;;;MAK1EuH,OAAO,CACH;QACIA,OAAO,CACHhI,WACA,SACA,QACA,QACAU,qBACAD,gBAAgB;MAEvB,CAAA;;;;;MAML,aAAa,CAAC;QAAE,aAAasD,0BAA2B;MAAA,CAAE;;;;;MAK1D,iBAAiB,CAAC;QAAEkE,KAAKjE,2BAA4B;MAAA,CAAE;;;;;MAKvD,aAAa,CAAC;QAAE,aAAaE,0BAA2B;MAAA,CAAE;;;;;MAK1D,WAAW,CAAC;QAAE,WAAWA,0BAA2B;MAAA,CAAE;;;;;MAKtD,aAAa,CAAC;QAAE,aAAaH,0BAA2B;MAAA,CAAE;;;;;MAK1D,iBAAiB,CAAC;QAAEmE,KAAKlE,2BAA4B;MAAA,CAAE;;;;;MAKvD,aAAa,CAAC;QAAE,aAAaE,0BAA2B;MAAA,CAAE;;;;;MAK1D,WAAW,CAAC;QAAE,WAAWA,0BAA2B;MAAA,CAAE;;;;;MAKtD,aAAa,CAAC;QAAE,aAAa,CAAC,OAAO,OAAO,SAAS,aAAa,WAAW;OAAG;;;;;MAKhF,aAAa,CAAC;QAAE,aAAaC,sBAAuB;MAAA,CAAE;;;;;MAKtD,aAAa,CAAC;QAAE,aAAaA,sBAAuB;MAAA,CAAE;;;;;MAKtDgE,KAAK,CAAC;QAAEA,KAAKtE,wBAAyB;MAAA,CAAE;;;;;MAKxC,SAAS,CAAC;QAAE,SAASA,wBAAyB;MAAA,CAAE;;;;;MAKhD,SAAS,CAAC;QAAE,SAASA,wBAAyB;MAAA,CAAE;;;;;MAKhD,mBAAmB,CAAC;QAAEuE,SAAS,CAAC,GAAGhE,sBAAuB,GAAE,QAAQ;OAAG;;;;;MAKvE,iBAAiB,CAAC;QAAE,iBAAiB,CAAC,GAAGC,wBAAyB,GAAE,QAAQ;OAAG;;;;;MAK/E,gBAAgB,CAAC;QAAE,gBAAgB,CAAC,QAAQ,GAAGA,wBAAyB,CAAA;OAAG;;;;;MAK3E,iBAAiB,CAAC;QAAEgE,SAAS,CAAC,UAAU,GAAGjE,sBAAuB,CAAA;OAAG;;;;;MAKrE,eAAe,CAAC;QAAEkE,OAAO,CAAC,GAAGjE,wBAAyB,GAAE;UAAEkE,UAAU,CAAC,IAAI,MAAM;QAAC,CAAE;MAAC,CAAE;;;;;MAKrF,cAAc,CACV;QAAEC,MAAM,CAAC,QAAQ,GAAGnE,wBAAyB,GAAE;UAAEkE,UAAU,CAAC,IAAI,MAAM;QAAC,CAAE;MAAG,CAAA;;;;;MAMhF,iBAAiB,CAAC;QAAE,iBAAiBnE,sBAAuB;MAAA,CAAE;;;;;MAK9D,eAAe,CAAC;QAAE,eAAe,CAAC,GAAGC,wBAAyB,GAAE,UAAU;OAAG;;;;;MAK7E,cAAc,CAAC;QAAE,cAAc,CAAC,QAAQ,GAAGA,wBAAyB,CAAA;OAAG;;;;;;MAMvEoE,GAAG,CAAC;QAAEA,GAAG5E,wBAAyB;MAAA,CAAE;;;;;MAKpC6E,IAAI,CAAC;QAAEA,IAAI7E,wBAAyB;MAAA,CAAE;;;;;MAKtC8E,IAAI,CAAC;QAAEA,IAAI9E,wBAAyB;MAAA,CAAE;;;;;MAKtC+E,IAAI,CAAC;QAAEA,IAAI/E,wBAAyB;MAAA,CAAE;;;;;MAKtCgF,IAAI,CAAC;QAAEA,IAAIhF,wBAAyB;MAAA,CAAE;;;;;MAKtCiF,IAAI,CAAC;QAAEA,IAAIjF,wBAAyB;MAAA,CAAE;;;;;MAKtCkF,IAAI,CAAC;QAAEA,IAAIlF,wBAAyB;MAAA,CAAE;;;;;MAKtCmF,IAAI,CAAC;QAAEA,IAAInF,wBAAyB;MAAA,CAAE;;;;;MAKtCoF,IAAI,CAAC;QAAEA,IAAIpF,wBAAyB;MAAA,CAAE;;;;;MAKtCqF,GAAG,CAAC;QAAEA,GAAG5E,YAAa;MAAA,CAAE;;;;;MAKxB6E,IAAI,CAAC;QAAEA,IAAI7E,YAAa;MAAA,CAAE;;;;;MAK1B8E,IAAI,CAAC;QAAEA,IAAI9E,YAAa;MAAA,CAAE;;;;;MAK1B+E,IAAI,CAAC;QAAEA,IAAI/E,YAAa;MAAA,CAAE;;;;;MAK1BgF,IAAI,CAAC;QAAEA,IAAIhF,YAAa;MAAA,CAAE;;;;;MAK1BiF,IAAI,CAAC;QAAEA,IAAIjF,YAAa;MAAA,CAAE;;;;;MAK1BkF,IAAI,CAAC;QAAEA,IAAIlF,YAAa;MAAA,CAAE;;;;;MAK1BmF,IAAI,CAAC;QAAEA,IAAInF,YAAa;MAAA,CAAE;;;;;MAK1BoF,IAAI,CAAC;QAAEA,IAAIpF,YAAa;MAAA,CAAE;;;;;MAK1B,WAAW,CAAC;QAAE,WAAWT,wBAAyB;MAAA,CAAE;;;;;MAKpD,mBAAmB,CAAC,iBAAiB;;;;;MAKrC,WAAW,CAAC;QAAE,WAAWA,wBAAyB;MAAA,CAAE;;;;;MAKpD,mBAAmB,CAAC,iBAAiB;;;;;;;;MAUrCiB,MAAM,CAAC;QAAEA,MAAMP,YAAa;MAAA,CAAE;;;;;MAK9BoF,GAAG,CAAC;QAAEA,GAAG,CAAC/G,gBAAgB,UAAU,GAAG2B,YAAa,CAAA;OAAG;;;;;MAKvD,SAAS,CACL;QACI,SAAS;UACL3B;UACA;;UAEA;UACA,GAAG2B,YAAa;QAAA;MAEvB,CAAA;;;;;MAML,SAAS,CACL;QACI,SAAS;UACL3B;UACA;UACA;;UAEA;;UAEA;YAAEgH,QAAQ,CAACjH,eAAe;UAAG;UAC7B,GAAG4B,YAAa;QAAA;MAEvB,CAAA;;;;;MAMLsF,GAAG,CAAC;QAAEA,GAAG,CAAC,UAAU,MAAM,GAAGtF,YAAa,CAAA;OAAG;;;;;MAK7C,SAAS,CAAC;QAAE,SAAS,CAAC,UAAU,MAAM,QAAQ,GAAGA,YAAa,CAAA;OAAG;;;;;MAKjE,SAAS,CAAC;QAAE,SAAS,CAAC,UAAU,MAAM,GAAGA,YAAa,CAAA;OAAG;;;;;;;;MAUzD,aAAa,CACT;QAAEgC,MAAM,CAAC,QAAQhE,WAAWf,2BAA2BV,iBAAiB;MAAG,CAAA;;;;;MAM/E,kBAAkB,CAAC,eAAe,sBAAsB;;;;;MAKxD,cAAc,CAAC,UAAU,YAAY;;;;;MAKrC,eAAe,CAAC;QAAEmF,MAAM,CAACzD,iBAAiB9B,qBAAqBM,iBAAiB;OAAG;;;;;MAKnF,gBAAgB,CACZ;QACI,gBAAgB,CACZ,mBACA,mBACA,aACA,kBACA,UACA,iBACA,YACA,kBACA,kBACAf,WACAQ,gBAAgB;MAEvB,CAAA;;;;;MAML,eAAe,CAAC;QAAEwF,MAAM,CAACvE,+BAA+BjB,kBAAkB6B,SAAS;OAAG;;;;;MAKtF,cAAc,CAAC,aAAa;;;;;MAK5B,eAAe,CAAC,SAAS;;;;;MAKzB,oBAAoB,CAAC,cAAc;;;;;MAKnC,cAAc,CAAC,eAAe,eAAe;;;;;MAK7C,eAAe,CAAC,qBAAqB,cAAc;;;;;MAKnD,gBAAgB,CAAC,sBAAsB,mBAAmB;;;;;MAK1DkE,UAAU,CAAC;QAAEA,UAAU,CAAC/D,eAAe/B,qBAAqBD,gBAAgB;OAAG;;;;;MAK/E,cAAc,CACV;QAAE,cAAc,CAACZ,UAAU,QAAQa,qBAAqBM,iBAAiB;MAAG,CAAA;;;;;MAMhFkF,SAAS,CACL;QACIA,SAAS;;UAELxD;UACA,GAAGmB,wBAAyB;QAAA;MAEnC,CAAA;;;;;MAML,cAAc,CAAC;QAAE,cAAc,CAAC,QAAQnD,qBAAqBD,gBAAgB;OAAG;;;;;MAKhF,uBAAuB,CAAC;QAAEqJ,MAAM,CAAC,UAAU,SAAS;MAAC,CAAE;;;;;MAKvD,mBAAmB,CACf;QAAEA,MAAM,CAAC,QAAQ,WAAW,QAAQpJ,qBAAqBD,gBAAgB;MAAG,CAAA;;;;;MAMhF,kBAAkB,CAAC;QAAE8F,MAAM,CAAC,QAAQ,UAAU,SAAS,WAAW,SAAS,KAAK;OAAG;;;;;;MAMnF,qBAAqB,CAAC;QAAEwD,aAAavF,WAAY;MAAA,CAAE;;;;;MAKnD,cAAc,CAAC;QAAE+B,MAAM/B,WAAY;MAAA,CAAE;;;;;MAKrC,mBAAmB,CAAC,aAAa,YAAY,gBAAgB,cAAc;;;;;MAK3E,yBAAyB,CAAC;QAAEwF,YAAY,CAAC,GAAG9E,eAAgB,GAAE,MAAM;OAAG;;;;;MAKvE,6BAA6B,CACzB;QACI8E,YAAY,CACRnK,UACA,aACA,QACAa,qBACAI,iBAAiB;MAExB,CAAA;;;;;MAML,yBAAyB,CAAC;QAAEkJ,YAAYxF,WAAY;MAAA,CAAE;;;;;MAKtD,oBAAoB,CAChB;QAAE,oBAAoB,CAAC3E,UAAU,QAAQa,qBAAqBD,gBAAgB;MAAG,CAAA;;;;;MAMrF,kBAAkB,CAAC,aAAa,aAAa,cAAc,aAAa;;;;;MAKxE,iBAAiB,CAAC,YAAY,iBAAiB,WAAW;;;;;MAK1D,aAAa,CAAC;QAAE8F,MAAM,CAAC,QAAQ,UAAU,WAAW,QAAQ;OAAG;;;;;MAK/D0D,QAAQ,CAAC;QAAEA,QAAQpG,wBAAyB;MAAA,CAAE;;;;;MAK9C,kBAAkB,CACd;QACIqG,OAAO,CACH,YACA,OACA,UACA,UACA,YACA,eACA,OACA,SACAxJ,qBACAD,gBAAgB;MAEvB,CAAA;;;;;MAML0J,YAAY,CACR;QAAEA,YAAY,CAAC,UAAU,UAAU,OAAO,YAAY,YAAY,cAAc;MAAG,CAAA;;;;;MAMvFC,OAAO,CAAC;QAAEA,OAAO,CAAC,UAAU,SAAS,OAAO,MAAM;OAAG;;;;;MAKrDC,MAAM,CAAC;QAAEA,MAAM,CAAC,cAAc,YAAY,QAAQ;OAAG;;;;;MAKrDC,SAAS,CAAC;QAAEA,SAAS,CAAC,QAAQ,UAAU,MAAM;OAAG;;;;;MAKjDjC,SAAS,CAAC;QAAEA,SAAS,CAAC,QAAQ3H,qBAAqBD,gBAAgB;OAAG;;;;;;;;MAUtE,iBAAiB,CAAC;QAAE8J,IAAI,CAAC,SAAS,SAAS,QAAQ;OAAG;;;;;MAKtD,WAAW,CAAC;QAAE,WAAW,CAAC,UAAU,WAAW,WAAW,MAAM;OAAG;;;;;MAKnE,aAAa,CAAC;QAAE,aAAa,CAAC,UAAU,WAAW,SAAS;OAAG;;;;;MAK/D,eAAe,CAAC;QAAEA,IAAI9F,gBAAiB;MAAA,CAAE;;;;;MAKzC,aAAa,CAAC;QAAE8F,IAAI5F,cAAe;MAAA,CAAE;;;;;MAKrC,WAAW,CAAC;QAAE4F,IAAI1F,YAAa;MAAA,CAAE;;;;;MAKjC,YAAY,CACR;QACI0F,IAAI,CACA,QACA;UACIC,QAAQ,CACJ;YAAEC,IAAI,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;UAAG,GACpDzK,WACAU,qBACAD,gBAAgB;UAEpBiK,QAAQ,CAAC,IAAIhK,qBAAqBD,gBAAgB;UAClDkK,OAAO,CAAC3K,WAAWU,qBAAqBD,gBAAgB;QAC3D,GACDqB,0BACAV,gBAAgB;MAEvB,CAAA;;;;;MAML,YAAY,CAAC;QAAEmJ,IAAI/F,WAAY;MAAA,CAAE;;;;;MAKjC,qBAAqB,CAAC;QAAEoG,MAAM7F,0BAA2B;MAAA,CAAE;;;;;MAK3D,oBAAoB,CAAC;QAAE8F,KAAK9F,0BAA2B;MAAA,CAAE;;;;;MAKzD,mBAAmB,CAAC;QAAE0F,IAAI1F,0BAA2B;MAAA,CAAE;;;;;MAKvD,iBAAiB,CAAC;QAAE6F,MAAMpG,WAAY;MAAA,CAAE;;;;;MAKxC,gBAAgB,CAAC;QAAEqG,KAAKrG,WAAY;MAAA,CAAE;;;;;MAKtC,eAAe,CAAC;QAAEiG,IAAIjG,WAAY;MAAA,CAAE;;;;;;;;MAUpCsG,SAAS,CAAC;QAAEA,SAAS9F,YAAa;MAAA,CAAE;;;;;MAKpC,aAAa,CAAC;QAAE,aAAaA,YAAa;MAAA,CAAE;;;;;MAK5C,aAAa,CAAC;QAAE,aAAaA,YAAa;MAAA,CAAE;;;;;MAK5C,aAAa,CAAC;QAAE,aAAaA,YAAa;MAAA,CAAE;;;;;MAK5C,aAAa,CAAC;QAAE,aAAaA,YAAa;MAAA,CAAE;;;;;MAK5C,aAAa,CAAC;QAAE,aAAaA,YAAa;MAAA,CAAE;;;;;MAK5C,aAAa,CAAC;QAAE,aAAaA,YAAa;MAAA,CAAE;;;;;MAK5C,cAAc,CAAC;QAAE,cAAcA,YAAa;MAAA,CAAE;;;;;MAK9C,cAAc,CAAC;QAAE,cAAcA,YAAa;MAAA,CAAE;;;;;MAK9C,cAAc,CAAC;QAAE,cAAcA,YAAa;MAAA,CAAE;;;;;MAK9C,cAAc,CAAC;QAAE,cAAcA,YAAa;MAAA,CAAE;;;;;MAK9C,cAAc,CAAC;QAAE,cAAcA,YAAa;MAAA,CAAE;;;;;MAK9C,cAAc,CAAC;QAAE,cAAcA,YAAa;MAAA,CAAE;;;;;MAK9C,cAAc,CAAC;QAAE,cAAcA,YAAa;MAAA,CAAE;;;;;MAK9C,cAAc,CAAC;QAAE,cAAcA,YAAa;MAAA,CAAE;;;;;MAK9C,YAAY,CAAC;QAAE+F,QAAQ9F,iBAAkB;MAAA,CAAE;;;;;MAK3C,cAAc,CAAC;QAAE,YAAYA,iBAAkB;MAAA,CAAE;;;;;MAKjD,cAAc,CAAC;QAAE,YAAYA,iBAAkB;MAAA,CAAE;;;;;MAKjD,cAAc,CAAC;QAAE,YAAYA,iBAAkB;MAAA,CAAE;;;;;MAKjD,cAAc,CAAC;QAAE,YAAYA,iBAAkB;MAAA,CAAE;;;;;MAKjD,cAAc,CAAC;QAAE,YAAYA,iBAAkB;MAAA,CAAE;;;;;MAKjD,cAAc,CAAC;QAAE,YAAYA,iBAAkB;MAAA,CAAE;;;;;MAKjD,cAAc,CAAC;QAAE,YAAYA,iBAAkB;MAAA,CAAE;;;;;MAKjD,cAAc,CAAC;QAAE,YAAYA,iBAAkB;MAAA,CAAE;;;;;MAKjD,YAAY,CAAC;QAAE,YAAYA,iBAAkB;MAAA,CAAE;;;;;MAK/C,oBAAoB,CAAC,kBAAkB;;;;;MAKvC,YAAY,CAAC;QAAE,YAAYA,iBAAkB;MAAA,CAAE;;;;;MAK/C,oBAAoB,CAAC,kBAAkB;;;;;MAKvC,gBAAgB,CAAC;QAAE8F,QAAQ,CAAC,GAAG7F,eAAc,GAAI,UAAU,MAAM;OAAG;;;;;MAKpE,gBAAgB,CAAC;QAAE8F,QAAQ,CAAC,GAAG9F,eAAc,GAAI,UAAU,MAAM;OAAG;;;;;MAKpE,gBAAgB,CAAC;QAAE6F,QAAQvG,WAAY;MAAA,CAAE;;;;;MAKzC,kBAAkB,CAAC;QAAE,YAAYA,WAAY;MAAA,CAAE;;;;;MAK/C,kBAAkB,CAAC;QAAE,YAAYA,WAAY;MAAA,CAAE;;;;;MAK/C,kBAAkB,CAAC;QAAE,YAAYA,WAAY;MAAA,CAAE;;;;;MAK/C,kBAAkB,CAAC;QAAE,YAAYA,WAAY;MAAA,CAAE;;;;;MAK/C,kBAAkB,CAAC;QAAE,YAAYA,WAAY;MAAA,CAAE;;;;;MAK/C,kBAAkB,CAAC;QAAE,YAAYA,WAAY;MAAA,CAAE;;;;;MAK/C,kBAAkB,CAAC;QAAE,YAAYA,WAAY;MAAA,CAAE;;;;;MAK/C,kBAAkB,CAAC;QAAE,YAAYA,WAAY;MAAA,CAAE;;;;;MAK/C,gBAAgB,CAAC;QAAEwG,QAAQxG,WAAY;MAAA,CAAE;;;;;MAKzC,iBAAiB,CAAC;QAAEyG,SAAS,CAAC,GAAG/F,eAAc,GAAI,QAAQ,QAAQ;OAAG;;;;;MAKtE,kBAAkB,CACd;QAAE,kBAAkB,CAACrF,UAAUa,qBAAqBD,gBAAgB;MAAG,CAAA;;;;;MAM3E,aAAa,CACT;QAAEwK,SAAS,CAAC,IAAIpL,UAAU2B,2BAA2BV,iBAAiB;MAAG,CAAA;;;;;MAM7E,iBAAiB,CAAC;QAAEmK,SAASzG,WAAY;MAAA,CAAE;;;;;;;;MAU3C6B,QAAQ,CACJ;QACIA,QAAQ;;UAEJ;UACA;UACAtD;UACAhB;UACAT;QAAiB;MAExB,CAAA;;;;;MAML,gBAAgB,CAAC;QAAE+E,QAAQ7B,WAAY;MAAA,CAAE;;;;;MAKzC,gBAAgB,CACZ;QACI,gBAAgB,CACZ,QACAxB,kBACAjB,2BACAT,iBAAiB;MAExB,CAAA;;;;;MAML,sBAAsB,CAAC;QAAE,gBAAgBkD,WAAY;MAAA,CAAE;;;;;MAKvD,UAAU,CAAC;QAAE0G,MAAMjG,iBAAkB;MAAA,CAAE;;;;;;;MAOvC,gBAAgB,CAAC,YAAY;;;;;MAK7B,cAAc,CAAC;QAAEiG,MAAM1G,WAAY;MAAA,CAAE;;;;;;;MAOrC,iBAAiB,CAAC;QAAE,eAAe,CAAC3E,UAAUiB,iBAAiB;MAAC,CAAE;;;;;;;MAOlE,qBAAqB,CAAC;QAAE,eAAe0D,WAAY;MAAA,CAAE;;;;;MAKrD,gBAAgB,CAAC;QAAE,cAAcS,iBAAkB;MAAA,CAAE;;;;;MAKrD,oBAAoB,CAAC;QAAE,cAAcT,WAAY;MAAA,CAAE;;;;;MAKnD,eAAe,CACX;QACI,eAAe,CACX,QACAvB,iBACAlB,2BACAT,iBAAiB;MAExB,CAAA;;;;;MAML,qBAAqB,CAAC;QAAE,eAAekD,WAAY;MAAA,CAAE;;;;;MAKrD2G,SAAS,CAAC;QAAEA,SAAS,CAACtL,UAAUa,qBAAqBD,gBAAgB;OAAG;;;;;MAKxE,aAAa,CAAC;QAAE,aAAa,CAAC,GAAG0E,eAAc,GAAI,eAAe,cAAc;OAAG;;;;;MAKnF,YAAY,CAAC;QAAE,YAAYA,eAAgB;MAAA,CAAE;;;;;MAK7C,aAAa,CACT;QAAE,aAAa,CAAC,UAAU,WAAW,WAAW,QAAQ,UAAU,MAAM;MAAG,GAC3E,cAAc;;;;;MAMlB,kBAAkB,CAAC;QAAEiG,MAAM,CAAC,OAAO,YAAY,aAAa,SAAS;OAAG;;;;;MAKxE,yBAAyB,CAAC;QAAE,eAAe,CAACvL,QAAQ;MAAC,CAAE;MACvD,8BAA8B,CAAC;QAAE,oBAAoBuF,uBAAwB;MAAA,CAAE;MAC/E,4BAA4B,CAAC;QAAE,kBAAkBA,uBAAwB;MAAA,CAAE;MAC3E,gCAAgC,CAAC;QAAE,oBAAoBZ,WAAY;MAAA,CAAE;MACrE,8BAA8B,CAAC;QAAE,kBAAkBA,WAAY;MAAA,CAAE;MACjE,yBAAyB,CAAC;QAAE,eAAeY,uBAAwB;MAAA,CAAE;MACrE,uBAAuB,CAAC;QAAE,aAAaA,uBAAwB;MAAA,CAAE;MACjE,2BAA2B,CAAC;QAAE,eAAeZ,WAAY;MAAA,CAAE;MAC3D,yBAAyB,CAAC;QAAE,aAAaA,WAAY;MAAA,CAAE;MACvD,yBAAyB,CAAC;QAAE,eAAeY,uBAAwB;MAAA,CAAE;MACrE,uBAAuB,CAAC;QAAE,aAAaA,uBAAwB;MAAA,CAAE;MACjE,2BAA2B,CAAC;QAAE,eAAeZ,WAAY;MAAA,CAAE;MAC3D,yBAAyB,CAAC;QAAE,aAAaA,WAAY;MAAA,CAAE;MACvD,yBAAyB,CAAC;QAAE,eAAeY,uBAAwB;MAAA,CAAE;MACrE,uBAAuB,CAAC;QAAE,aAAaA,uBAAwB;MAAA,CAAE;MACjE,2BAA2B,CAAC;QAAE,eAAeZ,WAAY;MAAA,CAAE;MAC3D,yBAAyB,CAAC;QAAE,aAAaA,WAAY;MAAA,CAAE;MACvD,yBAAyB,CAAC;QAAE,eAAeY,uBAAwB;MAAA,CAAE;MACrE,uBAAuB,CAAC;QAAE,aAAaA,uBAAwB;MAAA,CAAE;MACjE,2BAA2B,CAAC;QAAE,eAAeZ,WAAY;MAAA,CAAE;MAC3D,yBAAyB,CAAC;QAAE,aAAaA,WAAY;MAAA,CAAE;MACvD,yBAAyB,CAAC;QAAE,eAAeY,uBAAwB;MAAA,CAAE;MACrE,uBAAuB,CAAC;QAAE,aAAaA,uBAAwB;MAAA,CAAE;MACjE,2BAA2B,CAAC;QAAE,eAAeZ,WAAY;MAAA,CAAE;MAC3D,yBAAyB,CAAC;QAAE,aAAaA,WAAY;MAAA,CAAE;MACvD,yBAAyB,CAAC;QAAE,eAAeY,uBAAwB;MAAA,CAAE;MACrE,uBAAuB,CAAC;QAAE,aAAaA,uBAAwB;MAAA,CAAE;MACjE,2BAA2B,CAAC;QAAE,eAAeZ,WAAY;MAAA,CAAE;MAC3D,yBAAyB,CAAC;QAAE,aAAaA,WAAY;MAAA,CAAE;MACvD,qBAAqB,CAAC;QAAE,eAAe,CAAC9D,qBAAqBD,gBAAgB;MAAC,CAAE;MAChF,8BAA8B,CAAC;QAAE,oBAAoB2E,uBAAwB;MAAA,CAAE;MAC/E,4BAA4B,CAAC;QAAE,kBAAkBA,uBAAwB;MAAA,CAAE;MAC3E,gCAAgC,CAAC;QAAE,oBAAoBZ,WAAY;MAAA,CAAE;MACrE,8BAA8B,CAAC;QAAE,kBAAkBA,WAAY;MAAA,CAAE;MACjE,2BAA2B,CAAC;QAAE,eAAe,CAAC,UAAU,SAAS;MAAC,CAAE;MACpE,0BAA0B,CACtB;QAAE,eAAe,CAAC;UAAE6G,SAAS,CAAC,QAAQ,QAAQ;UAAGC,UAAU,CAAC,QAAQ,QAAQ;QAAG,CAAA;MAAG,CAAA;MAEtF,yBAAyB,CAAC;QAAE,kBAAkB7H,cAAe;MAAA,CAAE;MAC/D,wBAAwB,CAAC;QAAE,cAAc,CAAC5D,QAAQ;MAAC,CAAE;MACrD,6BAA6B,CAAC;QAAE,mBAAmBuF,uBAAwB;MAAA,CAAE;MAC7E,2BAA2B,CAAC;QAAE,iBAAiBA,uBAAwB;MAAA,CAAE;MACzE,+BAA+B,CAAC;QAAE,mBAAmBZ,WAAY;MAAA,CAAE;MACnE,6BAA6B,CAAC;QAAE,iBAAiBA,WAAY;MAAA,CAAE;;;;;MAK/D,aAAa,CAAC;QAAE4G,MAAM,CAAC,SAAS,aAAa,OAAO;OAAG;;;;;MAKvD,eAAe,CACX;QAAE,eAAe,CAAC,UAAU,WAAW,WAAW,QAAQ,UAAU,MAAM;MAAG,CAAA;;;;;MAMjF,iBAAiB,CAAC;QAAEA,MAAM3G,gBAAiB;MAAA,CAAE;;;;;MAK7C,eAAe,CAAC;QAAE2G,MAAMzG,cAAe;MAAA,CAAE;;;;;MAKzC,aAAa,CAAC;QAAEyG,MAAMvG,YAAa;MAAA,CAAE;;;;;MAKrC,aAAa,CAAC;QAAE,aAAa,CAAC,SAAS,WAAW;MAAC,CAAE;;;;;MAKrD,cAAc,CAAC;QAAEuG,MAAM,CAAC,QAAQ1K,qBAAqBD,gBAAgB;OAAG;;;;;;;;MAUxE8K,QAAQ,CACJ;QACIA,QAAQ;;UAEJ;UACA;UACA7K;UACAD;QAAgB;MAEvB,CAAA;;;;;MAMLmF,MAAM,CAAC;QAAEA,MAAMP,UAAW;MAAA,CAAE;;;;;MAK5BmG,YAAY,CAAC;QAAEA,YAAY,CAAC3L,UAAUa,qBAAqBD,gBAAgB;OAAG;;;;;MAK9EgL,UAAU,CAAC;QAAEA,UAAU,CAAC5L,UAAUa,qBAAqBD,gBAAgB;OAAG;;;;;MAK1E,eAAe,CACX;QACI,eAAe;;UAEX;UACA;UACAyC;UACAnB;UACAT;QAAiB;MAExB,CAAA;;;;;MAML,qBAAqB,CAAC;QAAE,eAAekD,WAAY;MAAA,CAAE;;;;;MAKrDkH,WAAW,CAAC;QAAEA,WAAW,CAAC,IAAI7L,UAAUa,qBAAqBD,gBAAgB;OAAG;;;;;MAKhF,cAAc,CAAC;QAAE,cAAc,CAACZ,UAAUa,qBAAqBD,gBAAgB;OAAG;;;;;MAKlFkL,QAAQ,CAAC;QAAEA,QAAQ,CAAC,IAAI9L,UAAUa,qBAAqBD,gBAAgB;OAAG;;;;;MAK1EmL,UAAU,CAAC;QAAEA,UAAU,CAAC/L,UAAUa,qBAAqBD,gBAAgB;OAAG;;;;;MAK1EoL,OAAO,CAAC;QAAEA,OAAO,CAAC,IAAIhM,UAAUa,qBAAqBD,gBAAgB;OAAG;;;;;MAKxE,mBAAmB,CACf;QACI,mBAAmB;;UAEf;UACA;UACAC;UACAD;QAAgB;MAEvB,CAAA;;;;;MAML,iBAAiB,CAAC;QAAE,iBAAiB4E,UAAW;MAAA,CAAE;;;;;MAKlD,uBAAuB,CACnB;QAAE,uBAAuB,CAACxF,UAAUa,qBAAqBD,gBAAgB;MAAG,CAAA;;;;;MAMhF,qBAAqB,CACjB;QAAE,qBAAqB,CAACZ,UAAUa,qBAAqBD,gBAAgB;MAAG,CAAA;;;;;MAM9E,sBAAsB,CAClB;QAAE,sBAAsB,CAAC,IAAIZ,UAAUa,qBAAqBD,gBAAgB;MAAG,CAAA;;;;;MAMnF,uBAAuB,CACnB;QAAE,uBAAuB,CAACZ,UAAUa,qBAAqBD,gBAAgB;MAAG,CAAA;;;;;MAMhF,mBAAmB,CACf;QAAE,mBAAmB,CAAC,IAAIZ,UAAUa,qBAAqBD,gBAAgB;MAAG,CAAA;;;;;MAMhF,oBAAoB,CAChB;QAAE,oBAAoB,CAACZ,UAAUa,qBAAqBD,gBAAgB;MAAG,CAAA;;;;;MAM7E,qBAAqB,CACjB;QAAE,qBAAqB,CAACZ,UAAUa,qBAAqBD,gBAAgB;MAAG,CAAA;;;;;MAM9E,kBAAkB,CACd;QAAE,kBAAkB,CAAC,IAAIZ,UAAUa,qBAAqBD,gBAAgB;MAAG,CAAA;;;;;;;;MAW/E,mBAAmB,CAAC;QAAEsK,QAAQ,CAAC,YAAY,UAAU;MAAC,CAAE;;;;;MAKxD,kBAAkB,CAAC;QAAE,kBAAkBlH,wBAAyB;MAAA,CAAE;;;;;MAKlE,oBAAoB,CAAC;QAAE,oBAAoBA,wBAAyB;MAAA,CAAE;;;;;MAKtE,oBAAoB,CAAC;QAAE,oBAAoBA,wBAAyB;MAAA,CAAE;;;;;MAKtE,gBAAgB,CAAC;QAAEiI,OAAO,CAAC,QAAQ,OAAO;MAAC,CAAE;;;;;MAK7CC,SAAS,CAAC;QAAEA,SAAS,CAAC,OAAO,QAAQ;MAAC,CAAE;;;;;;;;MAUxCC,YAAY,CACR;QACIA,YAAY,CACR,IACA,OACA,UACA,WACA,UACA,aACA,QACAtL,qBACAD,gBAAgB;MAEvB,CAAA;;;;;MAML,uBAAuB,CAAC;QAAEuL,YAAY,CAAC,UAAU,UAAU;MAAC,CAAE;;;;;MAK9DC,UAAU,CAAC;QAAEA,UAAU,CAACpM,UAAU,WAAWa,qBAAqBD,gBAAgB;OAAG;;;;;MAKrFuF,MAAM,CACF;QAAEA,MAAM,CAAC,UAAU,WAAW1C,WAAW5C,qBAAqBD,gBAAgB;MAAG,CAAA;;;;;MAMrFyL,OAAO,CAAC;QAAEA,OAAO,CAACrM,UAAUa,qBAAqBD,gBAAgB;OAAG;;;;;MAKpEiF,SAAS,CAAC;QAAEA,SAAS,CAAC,QAAQnC,cAAc7C,qBAAqBD,gBAAgB;OAAG;;;;;;;;MAUpF0L,UAAU,CAAC;QAAEA,UAAU,CAAC,UAAU,SAAS;MAAC,CAAE;;;;;MAK9ChG,aAAa,CACT;QAAEA,aAAa,CAAC/C,kBAAkB1C,qBAAqBD,gBAAgB;MAAG,CAAA;;;;;MAM9E,sBAAsB,CAAC;QAAE,sBAAsBiD,2BAA4B;MAAA,CAAE;;;;;MAK7E0I,QAAQ,CAAC;QAAEA,QAAQ9G,YAAa;MAAA,CAAE;;;;;MAKlC,YAAY,CAAC;QAAE,YAAYA,YAAa;MAAA,CAAE;;;;;MAK1C,YAAY,CAAC;QAAE,YAAYA,YAAa;MAAA,CAAE;;;;;MAK1C,YAAY,CAAC;QAAE,YAAYA,YAAa;MAAA,CAAE;;;;;MAK1C+G,OAAO,CAAC;QAAEA,OAAO9G,WAAY;MAAA,CAAE;;;;;MAK/B,WAAW,CAAC;QAAE,WAAWA,WAAY;MAAA,CAAE;;;;;MAKvC,WAAW,CAAC;QAAE,WAAWA,WAAY;MAAA,CAAE;;;;;MAKvC,WAAW,CAAC;QAAE,WAAWA,WAAY;MAAA,CAAE;;;;;MAKvC,YAAY,CAAC,UAAU;;;;;MAKvB+G,MAAM,CAAC;QAAEA,MAAM9G,UAAW;MAAA,CAAE;;;;;MAK5B,UAAU,CAAC;QAAE,UAAUA,UAAW;MAAA,CAAE;;;;;MAKpC,UAAU,CAAC;QAAE,UAAUA,UAAW;MAAA,CAAE;;;;;MAKpC+G,WAAW,CACP;QAAEA,WAAW,CAAC7L,qBAAqBD,kBAAkB,IAAI,QAAQ,OAAO,KAAK;MAAG,CAAA;;;;;MAMpF,oBAAoB,CAAC;QAAE+L,QAAQ9I,2BAA4B;MAAA,CAAE;;;;;MAK7D,mBAAmB,CAAC;QAAE6I,WAAW,CAAC,MAAM,MAAM;MAAC,CAAE;;;;;MAKjDE,WAAW,CAAC;QAAEA,WAAWhH,eAAgB;MAAA,CAAE;;;;;MAK3C,eAAe,CAAC;QAAE,eAAeA,eAAgB;MAAA,CAAE;;;;;MAKnD,eAAe,CAAC;QAAE,eAAeA,eAAgB;MAAA,CAAE;;;;;MAKnD,eAAe,CAAC;QAAE,eAAeA,eAAgB;MAAA,CAAE;;;;;MAKnD,kBAAkB,CAAC,gBAAgB;;;;;;;;MAUnCiH,QAAQ,CAAC;QAAEA,QAAQlI,WAAY;MAAA,CAAE;;;;;MAKjCmI,YAAY,CAAC;QAAEA,YAAY,CAAC,QAAQ,MAAM;MAAC,CAAE;;;;;MAK7C,eAAe,CAAC;QAAEC,OAAOpI,WAAY;MAAA,CAAE;;;;;MAKvC,gBAAgB,CACZ;QAAEqI,QAAQ,CAAC,UAAU,QAAQ,SAAS,cAAc,aAAa,YAAY;MAAG,CAAA;;;;;MAMpFC,QAAQ,CACJ;QACIA,QAAQ,CACJ,QACA,WACA,WACA,QACA,QACA,QACA,QACA,eACA,QACA,gBACA,YACA,QACA,aACA,iBACA,SACA,QACA,WACA,QACA,YACA,cACA,cACA,cACA,YACA,YACA,YACA,YACA,aACA,aACA,aACA,aACA,aACA,aACA,eACA,eACA,WACA,YACApM,qBACAD,gBAAgB;MAEvB,CAAA;;;;;MAML,gBAAgB,CAAC;QAAE,gBAAgB,CAAC,SAAS,SAAS;MAAC,CAAE;;;;;MAKzD,kBAAkB,CAAC;QAAE,kBAAkB,CAAC,QAAQ,MAAM;MAAC,CAAE;;;;;MAKzDsM,QAAQ,CAAC;QAAEA,QAAQ,CAAC,QAAQ,IAAI,KAAK,GAAG;OAAG;;;;;MAK3C,mBAAmB,CAAC;QAAEC,QAAQ,CAAC,QAAQ,QAAQ;MAAC,CAAE;;;;;MAKlD,YAAY,CAAC;QAAE,YAAYnJ,wBAAyB;MAAA,CAAE;;;;;MAKtD,aAAa,CAAC;QAAE,aAAaA,wBAAyB;MAAA,CAAE;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAyB;MAAA,CAAE;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAyB;MAAA,CAAE;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAyB;MAAA,CAAE;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAyB;MAAA,CAAE;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAyB;MAAA,CAAE;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAyB;MAAA,CAAE;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAyB;MAAA,CAAE;;;;;MAKxD,YAAY,CAAC;QAAE,YAAYA,wBAAyB;MAAA,CAAE;;;;;MAKtD,aAAa,CAAC;QAAE,aAAaA,wBAAyB;MAAA,CAAE;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAyB;MAAA,CAAE;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAyB;MAAA,CAAE;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAyB;MAAA,CAAE;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAyB;MAAA,CAAE;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAyB;MAAA,CAAE;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAyB;MAAA,CAAE;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAyB;MAAA,CAAE;;;;;MAKxD,cAAc,CAAC;QAAEoJ,MAAM,CAAC,SAAS,OAAO,UAAU,YAAY;OAAG;;;;;MAKjE,aAAa,CAAC;QAAEA,MAAM,CAAC,UAAU,QAAQ;MAAC,CAAE;;;;;MAK5C,aAAa,CAAC;QAAEA,MAAM,CAAC,QAAQ,KAAK,KAAK,MAAM;OAAG;;;;;MAKlD,mBAAmB,CAAC;QAAEA,MAAM,CAAC,aAAa,WAAW;MAAC,CAAE;;;;;MAKxDC,OAAO,CAAC;QAAEA,OAAO,CAAC,QAAQ,QAAQ,cAAc;OAAG;;;;;MAKnD,WAAW,CAAC;QAAE,aAAa,CAAC,KAAK,QAAQ,OAAO;OAAG;;;;;MAKnD,WAAW,CAAC;QAAE,aAAa,CAAC,KAAK,MAAM,MAAM;OAAG;;;;;MAKhD,YAAY,CAAC,kBAAkB;;;;;MAK/BC,QAAQ,CAAC;QAAEA,QAAQ,CAAC,QAAQ,QAAQ,OAAO,MAAM;OAAG;;;;;MAKpD,eAAe,CACX;QACI,eAAe,CACX,QACA,UACA,YACA,aACAzM,qBACAD,gBAAgB;MAEvB,CAAA;;;;;;;;MAWL2M,MAAM,CAAC;QAAEA,MAAM,CAAC,QAAQ,GAAG5I,WAAY,CAAA;OAAG;;;;;MAK1C,YAAY,CACR;QACI6I,QAAQ,CACJxN,UACA2B,2BACAV,mBACAE,iBAAiB;MAExB,CAAA;;;;;MAMLqM,QAAQ,CAAC;QAAEA,QAAQ,CAAC,QAAQ,GAAG7I,WAAY,CAAA;OAAG;;;;;;;;MAU9C,uBAAuB,CAAC;QAAE,uBAAuB,CAAC,QAAQ,MAAM;MAAC,CAAE;IACtE;IACD5N,wBAAwB;MACpBqQ,UAAU,CAAC,cAAc,YAAY;MACrCC,YAAY,CAAC,gBAAgB,cAAc;MAC3CC,OAAO,CAAC,WAAW,WAAW,SAAS,OAAO,OAAO,SAAS,UAAU,MAAM;MAC9E,WAAW,CAAC,SAAS,MAAM;MAC3B,WAAW,CAAC,OAAO,QAAQ;MAC3BU,MAAM,CAAC,SAAS,QAAQ,QAAQ;MAChCM,KAAK,CAAC,SAAS,OAAO;MACtBM,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;MAClDC,IAAI,CAAC,MAAM,IAAI;MACfC,IAAI,CAAC,MAAM,IAAI;MACfO,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;MAClDC,IAAI,CAAC,MAAM,IAAI;MACfC,IAAI,CAAC,MAAM,IAAI;MACftE,MAAM,CAAC,KAAK,GAAG;MACf,aAAa,CAAC,SAAS;MACvB,cAAc,CACV,eACA,oBACA,cACA,eACA,cAAc;MAElB,eAAe,CAAC,YAAY;MAC5B,oBAAoB,CAAC,YAAY;MACjC,cAAc,CAAC,YAAY;MAC3B,eAAe,CAAC,YAAY;MAC5B,gBAAgB,CAAC,YAAY;MAC7B,cAAc,CAAC,WAAW,UAAU;MACpCgG,SAAS,CACL,aACA,aACA,aACA,aACA,aACA,aACA,cACA,cACA,cACA,cACA,cACA,cACA,cACA,YAAY;MAEhB,aAAa,CAAC,cAAc,YAAY;MACxC,aAAa,CAAC,cAAc,YAAY;MACxC,aAAa,CAAC,cAAc,YAAY;MACxC,aAAa,CAAC,cAAc,YAAY;MACxC,aAAa,CAAC,cAAc,YAAY;MACxC,aAAa,CAAC,cAAc,YAAY;MACxC,kBAAkB,CAAC,oBAAoB,kBAAkB;MACzD,YAAY,CACR,cACA,cACA,cACA,cACA,cACA,cACA,cACA,YAAY;MAEhB,cAAc,CAAC,cAAc,YAAY;MACzC,cAAc,CAAC,cAAc,YAAY;MACzC,gBAAgB,CACZ,kBACA,kBACA,kBACA,kBACA,kBACA,kBACA,kBACA,gBAAgB;MAEpB,kBAAkB,CAAC,kBAAkB,gBAAgB;MACrD,kBAAkB,CAAC,kBAAkB,gBAAgB;MACrD2B,WAAW,CAAC,eAAe,eAAe,gBAAgB;MAC1D,kBAAkB,CAAC,aAAa,eAAe,eAAe,aAAa;MAC3E,YAAY,CACR,aACA,aACA,aACA,aACA,aACA,aACA,aACA,WAAW;MAEf,aAAa,CAAC,aAAa,WAAW;MACtC,aAAa,CAAC,aAAa,WAAW;MACtC,YAAY,CACR,aACA,aACA,aACA,aACA,aACA,aACA,aACA,WAAW;MAEf,aAAa,CAAC,aAAa,WAAW;MACtC,aAAa,CAAC,aAAa,WAAW;MACtCS,OAAO,CAAC,WAAW,WAAW,UAAU;MACxC,WAAW,CAAC,OAAO;MACnB,WAAW,CAAC,OAAO;MACnB,YAAY,CAAC,OAAO;IACvB;IACDrW,gCAAgC;MAC5B,aAAa,CAAC,SAAS;IAC1B;IACDqF,yBAAyB,CACrB,KACA,MACA,SACA,YACA,UACA,mBACA,QACA,gBACA,cACA,UACA,eACA,WAAW;EAEoD;AAC3E;IChzEaoR,eAAeA,CACxBC,YACA;EACInT;EACAS;EACAC;EACA0S,SAAS,CAAE;EACXC,WAAW,CAAA;MAEf;AACAC,mBAAiBH,YAAY,aAAanT,SAAS;AACnDsT,mBAAiBH,YAAY,UAAU1S,MAAM;AAC7C6S,mBAAiBH,YAAY,8BAA8BzS,0BAA0B;AAErF6S,2BAAyBJ,WAAWzU,OAAO2U,SAAS3U,KAAK;AACzD6U,2BAAyBJ,WAAWxU,aAAa0U,SAAS1U,WAAW;AACrE4U,2BAAyBJ,WAAW3W,wBAAwB6W,SAAS7W,sBAAsB;AAC3F+W,2BACIJ,WAAW1W,gCACX4W,SAAS5W,8BAA8B;AAE3C6W,mBAAiBH,YAAY,2BAA2BE,SAASvR,uBAAuB;AAExF0R,wBAAsBL,WAAWzU,OAAO0U,OAAO1U,KAAK;AACpD8U,wBAAsBL,WAAWxU,aAAayU,OAAOzU,WAAW;AAChE6U,wBAAsBL,WAAW3W,wBAAwB4W,OAAO5W,sBAAsB;AACtFgX,wBACIL,WAAW1W,gCACX2W,OAAO3W,8BAA8B;AAEzCgX,uBAAqBN,YAAYC,QAAQ,yBAAyB;AAElE,SAAOD;AACX;AAEA,IAAMG,mBAAmBA,CACrBI,YACAC,aACAC,kBACA;AACA,MAAIA,kBAAkB/V,QAAW;AAC7B6V,eAAWC,WAAW,IAAIC;;AAElC;AAEA,IAAML,2BAA2BA,CAC7BG,YACAG,mBACA;AACA,MAAIA,gBAAgB;AAChB,eAAWtU,OAAOsU,gBAAgB;AAC9BP,uBAAiBI,YAAYnU,KAAKsU,eAAetU,GAAG,CAAC;;;AAGjE;AAEA,IAAMiU,wBAAwBA,CAC1BE,YACAI,gBACA;AACA,MAAIA,aAAa;AACb,eAAWvU,OAAOuU,aAAa;AAC3BL,2BAAqBC,YAAYI,aAAavU,GAAG;;;AAG7D;AAEA,IAAMkU,uBAAuBA,CACzBC,YACAI,aACAvU,QACA;AACA,QAAMwU,aAAaD,YAAYvU,GAAG;AAElC,MAAIwU,eAAelW,QAAW;AAC1B6V,eAAWnU,GAAG,IAAImU,WAAWnU,GAAG,IAAImU,WAAWnU,GAAG,EAAEyU,OAAOD,UAAU,IAAIA;;AAEjF;AC5EO,IAAME,sBAAsBA,CAI/BC,oBAMGC,iBAEH,OAAOD,oBAAoB,aACrBlQ,oBAAoBgE,kBAAkBkM,iBAAiB,GAAGC,YAAY,IACtEnQ,oBACI,MAAMkP,aAAalL,iBAAkB,GAAEkM,eAAe,GACtD,GAAGC,YAAY;ICpBhBC,UAAUpQ,oBAAoBgE,gBAAgB;", "names": ["CLASS_PART_SEPARATOR", "createClassGroupUtils", "config", "classMap", "createClassMap", "conflictingClassGroups", "conflictingClassGroupModifiers", "getClassGroupId", "className", "classParts", "split", "length", "shift", "getGroupRecursive", "getGroupIdForArbitraryProperty", "getConflictingClassGroupIds", "classGroupId", "hasPostfixModifier", "conflicts", "classPartObject", "currentClassPart", "nextClassPartObject", "nextPart", "get", "classGroupFromNextClassPart", "slice", "undefined", "validators", "classRest", "join", "find", "validator", "arbitraryPropertyRegex", "test", "arbitraryPropertyClassName", "exec", "property", "substring", "indexOf", "theme", "classGroups", "Map", "processClassesRecursively", "classGroup", "for<PERSON>ach", "classDefinition", "classPartObjectToEdit", "get<PERSON>art", "isThemeGetter", "push", "Object", "entries", "key", "path", "currentClassPartObject", "pathPart", "has", "set", "func", "createLruCache", "maxCacheSize", "cacheSize", "cache", "previousCache", "update", "value", "IMPORTANT_MODIFIER", "MODIFIER_SEPARATOR", "MODIFIER_SEPARATOR_LENGTH", "createParseClassName", "prefix", "experimentalParseClassName", "parseClassName", "modifiers", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modifierStart", "postfixModifierPosition", "index", "currentCharacter", "baseClassNameWithImportantModifier", "baseClassName", "stripImportantModifier", "hasImportantModifier", "maybePostfixModifierPosition", "fullPrefix", "parseClassNameOriginal", "startsWith", "isExternal", "endsWith", "createSortModifiers", "orderSensitiveModifiers", "fromEntries", "map", "modifier", "sortModifiers", "sortedModifiers", "unsortedModifiers", "isPositionSensitive", "sort", "createConfigUtils", "SPLIT_CLASSES_REGEX", "mergeClassList", "classList", "configUtils", "classGroupsInConflict", "classNames", "trim", "result", "originalClassName", "variantModifier", "modifierId", "classId", "includes", "conflictGroups", "i", "group", "twJoin", "argument", "resolvedValue", "string", "arguments", "toValue", "mix", "k", "createTailwindMerge", "createConfigFirst", "createConfigRest", "cacheGet", "cacheSet", "functionToCall", "initTailwindMerge", "reduce", "previousConfig", "createConfigCurrent", "tailwindMerge", "cachedResult", "callTailwindMerge", "apply", "fromTheme", "themeGetter", "arbitraryValueRegex", "arbitraryVariableRegex", "fractionRegex", "tshirtUnitRegex", "lengthUnitRegex", "colorFunctionRegex", "shadowRegex", "imageRegex", "isFraction", "isNumber", "Number", "isNaN", "isInteger", "isPercent", "isTshirtSize", "isAny", "is<PERSON>engthOnly", "isNever", "is<PERSON><PERSON>ow", "isImage", "isAnyNonArbitrary", "isArbitraryValue", "isArbitraryVariable", "isArbitrarySize", "getIsArbitraryValue", "isLabelSize", "isArbitraryLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isArbitraryNumber", "isLabelNumber", "isArbitraryPosition", "isLabelPosition", "isArbitraryImage", "isLabelImage", "isArbitraryShadow", "isLabel<PERSON><PERSON>ow", "isArbitraryVariableLength", "getIsArbitraryVariable", "isArbitraryVariableFamilyName", "isLabelFamilyName", "isArbitraryVariablePosition", "isArbitraryVariableSize", "isArbitraryVariableImage", "isArbitraryVariableShadow", "test<PERSON><PERSON><PERSON>", "testValue", "shouldMatchNoLabel", "label", "getDefaultConfig", "themeColor", "themeFont", "themeText", "themeFontWeight", "themeTracking", "themeLeading", "themeBreakpoint", "themeContainer", "themeSpacing", "themeRadius", "themeShadow", "themeInsetShadow", "themeTextShadow", "themeDropShadow", "themeBlur", "themePerspective", "themeAspect", "themeEase", "themeAnimate", "scaleBreak", "scalePosition", "scalePositionWithArbitrary", "scaleOverflow", "scaleOverscroll", "scaleUnambiguousSpacing", "scaleInset", "scaleGridTemplateColsRows", "scaleGridColRowStartAndEnd", "span", "scaleGridColRowStartOrEnd", "scaleGridAutoColsRows", "scaleAlignPrimaryAxis", "scaleAlignSecondaryAxis", "scaleMargin", "scaleSizing", "scaleColor", "scaleBgPosition", "position", "scaleBgRepeat", "repeat", "scaleBgSize", "size", "scaleGradientStopPosition", "scaleRadius", "scaleBorderWidth", "scaleLineStyle", "scaleBlendMode", "scaleMaskImagePosition", "scaleBlur", "scaleRotate", "scaleScale", "scaleSkew", "scaleTranslate", "animate", "aspect", "blur", "breakpoint", "color", "container", "ease", "font", "leading", "perspective", "radius", "shadow", "spacing", "text", "tracking", "columns", "box", "display", "sr", "float", "clear", "isolation", "object", "overflow", "overscroll", "inset", "start", "end", "top", "right", "bottom", "left", "visibility", "z", "basis", "flex", "grow", "shrink", "order", "col", "row", "gap", "justify", "content", "items", "baseline", "self", "p", "px", "py", "ps", "pe", "pt", "pr", "pb", "pl", "m", "mx", "my", "ms", "me", "mt", "mr", "mb", "ml", "w", "screen", "h", "list", "placeholder", "decoration", "indent", "align", "whitespace", "break", "wrap", "hyphens", "bg", "linear", "to", "radial", "conic", "from", "via", "rounded", "border", "divide", "outline", "ring", "opacity", "mask", "closest", "farthest", "filter", "brightness", "contrast", "grayscale", "invert", "saturate", "sepia", "table", "caption", "transition", "duration", "delay", "backface", "rotate", "scale", "skew", "transform", "origin", "translate", "accent", "appearance", "caret", "scheme", "cursor", "resize", "scroll", "snap", "touch", "select", "fill", "stroke", "mergeConfigs", "baseConfig", "extend", "override", "overrideProperty", "overrideConfigProperties", "mergeConfigProperties", "mergeArrayProperties", "baseObject", "override<PERSON><PERSON>", "overrideValue", "overrideObject", "mergeObject", "mergeValue", "concat", "extendTailwindMerge", "configExtension", "createConfig", "twMerge"]}