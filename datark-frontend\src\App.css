/* App-specific styles */
#root {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Enhanced animations for the app */
.app-fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth page transitions */
.page-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced focus styles for accessibility */
.focus-enhanced:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 0.375rem;
}
