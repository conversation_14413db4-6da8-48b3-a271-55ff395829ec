/**
 * Main App component for DatArk
 */
import React from 'react';
import { Toaster } from 'sonner';
import MainLayout from './components/Layout/MainLayout';
import './App.css';

function App() {
  return (
    <div className="App app-fade-in">
      <MainLayout />
      <Toaster
        position="top-right"
        toastOptions={{
          style: {
            background: 'hsl(var(--card))',
            color: 'hsl(var(--card-foreground))',
            border: '1px solid hsl(var(--border))',
          },
        }}
      />
    </div>
  );
}

export default App;
