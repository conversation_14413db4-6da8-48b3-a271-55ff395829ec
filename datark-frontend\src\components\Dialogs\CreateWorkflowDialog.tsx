/**
 * Dialog for creating new workflows
 */
import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Sparkles, Workflow as WorkflowIcon } from 'lucide-react';
import type { Workflow, WorkflowCreate } from '@/types';
import { workflowApi } from '@/services/api';
import { toast } from 'sonner';

interface CreateWorkflowDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onWorkflowCreated: (workflow: Workflow) => void;
}

const CreateWorkflowDialog: React.FC<CreateWorkflowDialogProps> = ({
  open,
  onOpenChange,
  onWorkflowCreated
}) => {
  const [formData, setFormData] = useState<WorkflowCreate>({
    name: '',
    description: ''
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('请输入工作流名称');
      return;
    }

    try {
      setLoading(true);
      const newWorkflow = await workflowApi.createWorkflow({
        name: formData.name.trim(),
        description: formData.description.trim() || undefined
      });
      
      onWorkflowCreated(newWorkflow);
      
      // Reset form
      setFormData({ name: '', description: '' });
    } catch (error) {
      console.error('Failed to create workflow:', error);
      toast.error('创建工作流失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({ name: '', description: '' });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[480px] animate-in">
        <DialogHeader className="text-center pb-2">
          <div className="w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center mx-auto mb-4 shadow-glow">
            <WorkflowIcon className="w-8 h-8 text-primary-foreground" />
          </div>
          <DialogTitle className="text-xl font-bold text-card-foreground">
            创建新工作流
          </DialogTitle>
          <DialogDescription className="text-muted-foreground leading-relaxed">
            创建一个新的数据生成工作流，开始构建您的数据模型和生成规则
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-semibold text-card-foreground">
                工作流名称 *
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="例如：用户数据生成"
                required
                className="input-field transition-all duration-200 focus:scale-101"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-semibold text-card-foreground">
                描述
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="描述这个工作流的用途和目标（可选）"
                rows={3}
                className="input-field resize-none transition-all duration-200 focus:scale-101"
              />
            </div>
          </div>

          <DialogFooter className="gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={loading}
              className="flex-1 transition-all duration-200 hover:scale-105"
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading || !formData.name.trim()}
              className="flex-1 btn-primary transition-all duration-200 hover:scale-105 disabled:opacity-50"
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <div className="loading-spinner w-4 h-4"></div>
                  创建中...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Sparkles className="w-4 h-4" />
                  创建工作流
                </div>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateWorkflowDialog;
