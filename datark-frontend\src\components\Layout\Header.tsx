/**
 * Enhanced Header component for DatArk application
 */
import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus, Settings, HelpCircle, Sparkles, Database } from 'lucide-react';

interface HeaderProps {
  onCreateWorkflow?: () => void;
}

const Header: React.FC<HeaderProps> = ({ onCreateWorkflow }) => {
  return (
    <header className="bg-card border-b border-border px-6 py-4 shadow-soft">
      <div className="flex items-center justify-between">
        {/* Enhanced Logo and title - Responsive */}
        <div className="flex items-center space-x-3 md:space-x-6">
          <div className="flex items-center space-x-2 md:space-x-3">
            {/* Enhanced logo with gradient */}
            <div className="relative">
              <div className="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center shadow-medium">
                <Database className="w-4 h-4 md:w-5 md:h-5 text-primary-foreground" />
              </div>
              <div className="absolute -top-1 -right-1 w-3 h-3 md:w-4 md:h-4 bg-gradient-to-br from-warning-400 to-warning-600 rounded-full flex items-center justify-center">
                <Sparkles className="w-2 h-2 md:w-2.5 md:h-2.5 text-white" />
              </div>
            </div>

            <div>
              <h1 className="text-lg md:text-2xl font-bold text-card-foreground bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                DatArk
              </h1>
              <p className="hidden sm:block text-xs text-muted-foreground font-medium">
                AI-Powered Data Generation
              </p>
            </div>
          </div>

          <div className="hidden lg:flex items-center space-x-2 px-3 py-1.5 bg-muted/30 rounded-full">
            <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
            <span className="text-xs text-muted-foreground font-medium">无代码数据生成平台</span>
          </div>
        </div>

        {/* Enhanced Actions - Responsive */}
        <div className="flex items-center space-x-2 md:space-x-3">
          <Button
            onClick={onCreateWorkflow}
            className="btn-primary flex items-center gap-1 md:gap-2 shadow-soft hover:shadow-medium transition-all duration-200 hover:scale-105"
            size="sm"
          >
            <Plus className="w-4 h-4" />
            <span className="hidden sm:inline">新建工作流</span>
            <span className="sm:hidden">新建</span>
          </Button>

          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              className="btn-ghost w-8 h-8 md:w-9 md:h-9 p-0 hover:bg-muted/50 transition-all duration-200"
            >
              <Settings className="w-3.5 h-3.5 md:w-4 md:h-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="btn-ghost w-8 h-8 md:w-9 md:h-9 p-0 hover:bg-muted/50 transition-all duration-200"
            >
              <HelpCircle className="w-3.5 h-3.5 md:w-4 md:h-4" />
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
