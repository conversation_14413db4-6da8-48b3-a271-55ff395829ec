/**
 * Main layout component for DatArk application
 */
import React, { useState, useEffect } from 'react';
import Header from './Header';
import Sidebar from './Sidebar';
import WorkflowEditor from '../WorkflowEditor/WorkflowEditor';
import CreateWorkflowDialog from '../Dialogs/CreateWorkflowDialog';
import type { Workflow } from '@/types';
import { workflowApi } from '@/services/api';
import { toast } from 'sonner';

const MainLayout: React.FC = () => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  // Load workflows on component mount
  useEffect(() => {
    loadWorkflows();
  }, []);

  const loadWorkflows = async () => {
    try {
      setLoading(true);
      const response = await workflowApi.getWorkflows();
      setWorkflows(response.workflows);
      
      // Select first workflow if none selected
      if (response.workflows.length > 0 && !selectedWorkflow) {
        setSelectedWorkflow(response.workflows[0]);
      }
    } catch (error) {
      console.error('Failed to load workflows:', error);
      toast.error('加载工作流失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateWorkflow = () => {
    setIsCreateDialogOpen(true);
  };

  const handleWorkflowCreated = (newWorkflow: Workflow) => {
    setWorkflows(prev => [newWorkflow, ...prev]);
    setSelectedWorkflow(newWorkflow);
    setIsCreateDialogOpen(false);
    toast.success('工作流创建成功');
  };

  const handleSelectWorkflow = (workflow: Workflow) => {
    setSelectedWorkflow(workflow);
  };

  const handleWorkflowUpdated = (updatedWorkflow: Workflow) => {
    setWorkflows(prev => 
      prev.map(w => w.id === updatedWorkflow.id ? updatedWorkflow : w)
    );
    setSelectedWorkflow(updatedWorkflow);
  };

  const handleWorkflowDeleted = (workflowId: number) => {
    setWorkflows(prev => prev.filter(w => w.id !== workflowId));
    if (selectedWorkflow?.id === workflowId) {
      const remainingWorkflows = workflows.filter(w => w.id !== workflowId);
      setSelectedWorkflow(remainingWorkflows.length > 0 ? remainingWorkflows[0] : null);
    }
  };

  if (loading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Header */}
      <Header onCreateWorkflow={handleCreateWorkflow} />
      
      {/* Main content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        <Sidebar
          workflows={workflows}
          selectedWorkflowId={selectedWorkflow?.id}
          onSelectWorkflow={handleSelectWorkflow}
          onCreateWorkflow={handleCreateWorkflow}
        />
        
        {/* Main editor area */}
        <div className="flex-1 flex flex-col">
          {selectedWorkflow ? (
            <WorkflowEditor
              workflow={selectedWorkflow}
              onWorkflowUpdated={handleWorkflowUpdated}
              onWorkflowDeleted={handleWorkflowDeleted}
            />
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">📊</span>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  选择一个工作流开始编辑
                </h3>
                <p className="text-gray-500 mb-4">
                  在左侧选择现有工作流，或创建一个新的工作流
                </p>
                <button
                  onClick={handleCreateWorkflow}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  创建新工作流
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Create workflow dialog */}
      <CreateWorkflowDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onWorkflowCreated={handleWorkflowCreated}
      />
    </div>
  );
};

export default MainLayout;
