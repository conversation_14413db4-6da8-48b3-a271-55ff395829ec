/**
 * Main layout component for DatArk application
 */
import React, { useState, useEffect } from 'react';
import Header from './Header';
import Sidebar from './Sidebar';
import WorkflowEditor from '../WorkflowEditor/WorkflowEditor';
import CreateWorkflowDialog from '../Dialogs/CreateWorkflowDialog';
import { Button } from '@/components/ui/button';
import { FileText, Plus } from 'lucide-react';
import type { Workflow } from '@/types';
import { workflowApi } from '@/services/api';
import { toast } from 'sonner';

const MainLayout: React.FC = () => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  // Load workflows on component mount
  useEffect(() => {
    loadWorkflows();
  }, []);

  const loadWorkflows = async () => {
    try {
      setLoading(true);
      const response = await workflowApi.getWorkflows();
      setWorkflows(response.workflows);
      
      // Select first workflow if none selected
      if (response.workflows.length > 0 && !selectedWorkflow) {
        setSelectedWorkflow(response.workflows[0]);
      }
    } catch (error) {
      console.error('Failed to load workflows:', error);
      toast.error('加载工作流失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateWorkflow = () => {
    setIsCreateDialogOpen(true);
  };

  const handleWorkflowCreated = (newWorkflow: Workflow) => {
    setWorkflows(prev => [newWorkflow, ...prev]);
    setSelectedWorkflow(newWorkflow);
    setIsCreateDialogOpen(false);
    toast.success('工作流创建成功');
  };

  const handleSelectWorkflow = (workflow: Workflow) => {
    setSelectedWorkflow(workflow);
  };

  const handleWorkflowUpdated = (updatedWorkflow: Workflow) => {
    setWorkflows(prev => 
      prev.map(w => w.id === updatedWorkflow.id ? updatedWorkflow : w)
    );
    setSelectedWorkflow(updatedWorkflow);
  };

  const handleWorkflowDeleted = (workflowId: number) => {
    setWorkflows(prev => prev.filter(w => w.id !== workflowId));
    if (selectedWorkflow?.id === workflowId) {
      const remainingWorkflows = workflows.filter(w => w.id !== workflowId);
      setSelectedWorkflow(remainingWorkflows.length > 0 ? remainingWorkflows[0] : null);
    }
  };

  if (loading) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="loading-spinner h-12 w-12 mx-auto mb-6"></div>
          <h3 className="text-lg font-semibold text-card-foreground mb-2">加载中</h3>
          <p className="text-muted-foreground">正在初始化应用程序...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <Header onCreateWorkflow={handleCreateWorkflow} />

      {/* Main content - Responsive */}
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar - Hidden on mobile when workflow is selected */}
        <div className={`${selectedWorkflow ? 'hidden md:flex' : 'flex'} flex-col`}>
          <Sidebar
            workflows={workflows}
            selectedWorkflowId={selectedWorkflow?.id}
            onSelectWorkflow={handleSelectWorkflow}
            onCreateWorkflow={handleCreateWorkflow}
          />
        </div>

        {/* Main editor area - Full width on mobile when workflow is selected */}
        <div className={`${selectedWorkflow ? 'flex' : 'hidden md:flex'} flex-1 flex-col overflow-hidden`}>
          {selectedWorkflow ? (
            <WorkflowEditor
              workflow={selectedWorkflow}
              onWorkflowUpdated={handleWorkflowUpdated}
              onWorkflowDeleted={handleWorkflowDeleted}
            />
          ) : (
            <div className="flex-1 flex items-center justify-center bg-muted/10">
              <div className="text-center px-6">
                <div className="w-20 h-20 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-6">
                  <FileText className="w-10 h-10 text-muted-foreground" />
                </div>
                <h3 className="text-lg md:text-xl font-semibold text-card-foreground mb-3">
                  选择一个工作流开始编辑
                </h3>
                <p className="text-muted-foreground mb-8 leading-relaxed max-w-md mx-auto">
                  从左侧选择一个现有工作流，或创建一个新的工作流来开始构建您的数据模型
                </p>
                <Button
                  onClick={handleCreateWorkflow}
                  className="btn-primary shadow-soft hover:shadow-medium transition-all duration-200 hover:scale-105"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  创建新工作流
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Create workflow dialog */}
      <CreateWorkflowDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onWorkflowCreated={handleWorkflowCreated}
      />
    </div>
  );
};

export default MainLayout;
