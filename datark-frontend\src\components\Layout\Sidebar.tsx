/**
 * Sidebar component for navigation and workflow list
 */
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Play, 
  CheckCircle, 
  XCircle, 
  Clock,
  MoreHorizontal 
} from 'lucide-react';
import type { Workflow } from '@/types';
import { WorkflowStatus } from '@/types';

interface SidebarProps {
  workflows: Workflow[];
  selectedWorkflowId?: number;
  onSelectWorkflow: (workflow: Workflow) => void;
  onCreateWorkflow: () => void;
}

const getStatusIcon = (status: WorkflowStatus) => {
  switch (status) {
    case WorkflowStatus.DRAFT:
      return <FileText className="w-4 h-4" />;
    case WorkflowStatus.RUNNING:
      return <Play className="w-4 h-4" />;
    case WorkflowStatus.COMPLETED:
      return <CheckCircle className="w-4 h-4" />;
    case WorkflowStatus.FAILED:
      return <XCircle className="w-4 h-4" />;
    default:
      return <Clock className="w-4 h-4" />;
  }
};

const getStatusColor = (status: WorkflowStatus) => {
  switch (status) {
    case WorkflowStatus.DRAFT:
      return 'bg-gray-100 text-gray-800';
    case WorkflowStatus.RUNNING:
      return 'bg-blue-100 text-blue-800';
    case WorkflowStatus.COMPLETED:
      return 'bg-green-100 text-green-800';
    case WorkflowStatus.FAILED:
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const Sidebar: React.FC<SidebarProps> = ({
  workflows,
  selectedWorkflowId,
  onSelectWorkflow,
  onCreateWorkflow
}) => {
  return (
    <div className="w-80 bg-gray-50 border-r border-gray-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">工作流</h2>
          <Button size="sm" onClick={onCreateWorkflow}>
            新建
          </Button>
        </div>
      </div>

      {/* Workflow list */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {workflows.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 text-sm mb-4">还没有工作流</p>
              <Button size="sm" onClick={onCreateWorkflow}>
                创建第一个工作流
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              {workflows.map((workflow) => (
                <div
                  key={workflow.id}
                  className={`p-3 rounded-lg cursor-pointer transition-colors ${
                    selectedWorkflowId === workflow.id
                      ? 'bg-blue-50 border border-blue-200'
                      : 'bg-white border border-gray-200 hover:bg-gray-50'
                  }`}
                  onClick={() => onSelectWorkflow(workflow)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        {getStatusIcon(workflow.status)}
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {workflow.name}
                        </h3>
                      </div>
                      
                      {workflow.description && (
                        <p className="text-xs text-gray-500 mb-2 line-clamp-2">
                          {workflow.description}
                        </p>
                      )}
                      
                      <div className="flex items-center justify-between">
                        <Badge 
                          variant="secondary" 
                          className={`text-xs ${getStatusColor(workflow.status)}`}
                        >
                          {workflow.status}
                        </Badge>
                        
                        <span className="text-xs text-gray-400">
                          {new Date(workflow.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    
                    <Button variant="ghost" size="sm" className="ml-2">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};

export default Sidebar;
