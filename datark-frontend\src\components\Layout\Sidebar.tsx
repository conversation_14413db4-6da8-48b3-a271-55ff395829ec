/**
 * Sidebar component for navigation and workflow list
 */
import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import {
  FileText,
  Play,
  CheckCircle,
  XCircle,
  Clock,
  MoreHorizontal,
  Plus
} from 'lucide-react';
import type { Workflow } from '@/types';
import { WorkflowStatus } from '@/types';

interface SidebarProps {
  workflows: Workflow[];
  selectedWorkflowId?: number;
  onSelectWorkflow: (workflow: Workflow) => void;
  onCreateWorkflow: () => void;
}

const getStatusIcon = (status: WorkflowStatus) => {
  switch (status) {
    case WorkflowStatus.DRAFT:
      return <FileText className="w-4 h-4" />;
    case WorkflowStatus.RUNNING:
      return <Play className="w-4 h-4" />;
    case WorkflowStatus.COMPLETED:
      return <CheckCircle className="w-4 h-4" />;
    case WorkflowStatus.FAILED:
      return <XCircle className="w-4 h-4" />;
    default:
      return <Clock className="w-4 h-4" />;
  }
};

const getStatusColor = (status: WorkflowStatus) => {
  switch (status) {
    case WorkflowStatus.DRAFT:
      return 'bg-gray-100 text-gray-800';
    case WorkflowStatus.RUNNING:
      return 'bg-blue-100 text-blue-800';
    case WorkflowStatus.COMPLETED:
      return 'bg-green-100 text-green-800';
    case WorkflowStatus.FAILED:
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const Sidebar: React.FC<SidebarProps> = ({
  workflows,
  selectedWorkflowId,
  onSelectWorkflow,
  onCreateWorkflow
}) => {
  return (
    <div className="w-full md:w-80 bg-muted/20 border-r border-border flex flex-col">
      {/* Enhanced Header - Responsive */}
      <div className="p-4 md:p-6 border-b border-border bg-card/50">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-base md:text-lg font-semibold text-card-foreground">工作流</h2>
            <p className="hidden md:block text-xs text-muted-foreground mt-1">
              管理您的数据生成项目
            </p>
          </div>
          <Button
            size="sm"
            onClick={onCreateWorkflow}
            className="btn-primary shadow-soft hover:shadow-medium transition-all duration-200 hover:scale-105"
          >
            <Plus className="w-4 h-4 md:mr-1" />
            <span className="hidden md:inline">新建</span>
          </Button>
        </div>
      </div>

      {/* Enhanced Workflow list - Responsive */}
      <ScrollArea className="flex-1">
        <div className="p-3 md:p-4">
          {workflows.length === 0 ? (
            <div className="text-center py-8 md:py-12">
              <div className="w-12 h-12 md:w-16 md:h-16 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4 md:mb-6">
                <FileText className="w-6 h-6 md:w-8 md:h-8 text-muted-foreground" />
              </div>
              <h3 className="text-sm font-medium text-card-foreground mb-2">还没有工作流</h3>
              <p className="text-xs text-muted-foreground mb-4 md:mb-6 leading-relaxed px-2">
                创建您的第一个工作流，开始构建数据模型
              </p>
              <Button
                size="sm"
                onClick={onCreateWorkflow}
                className="btn-primary shadow-soft hover:shadow-medium transition-all duration-200 hover:scale-105"
              >
                <Plus className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">创建第一个工作流</span>
                <span className="sm:hidden">创建工作流</span>
              </Button>
            </div>
          ) : (
            <div className="space-y-2 md:space-y-3">
              {workflows.map((workflow) => (
                <div
                  key={workflow.id}
                  className={`card-elevated p-3 md:p-4 cursor-pointer transition-all duration-200 group touch-manipulation ${
                    selectedWorkflowId === workflow.id
                      ? 'border-primary shadow-glow bg-primary/5 scale-102'
                      : 'hover:border-primary/30 hover:shadow-medium hover:scale-101 active:scale-100'
                  }`}
                  onClick={() => onSelectWorkflow(workflow)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 md:space-x-3 mb-2">
                        <div className="flex-shrink-0">
                          {getStatusIcon(workflow.status)}
                        </div>
                        <h3 className="text-sm font-semibold text-card-foreground truncate">
                          {workflow.name}
                        </h3>
                      </div>

                      {workflow.description && (
                        <p className="text-xs text-muted-foreground mb-2 md:mb-3 line-clamp-2 leading-relaxed">
                          {workflow.description}
                        </p>
                      )}

                      <div className="flex items-center justify-between">
                        <Badge
                          variant="secondary"
                          className={`text-xs px-2 py-1 ${getStatusColor(workflow.status)}`}
                        >
                          {workflow.status}
                        </Badge>

                        <span className="hidden md:inline text-xs text-muted-foreground font-mono">
                          {new Date(workflow.created_at).toLocaleDateString('zh-CN')}
                        </span>
                      </div>
                    </div>

                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-2 md:ml-3 opacity-0 group-hover:opacity-100 md:transition-opacity duration-200 hover:bg-muted/50 w-8 h-8 p-0"
                    >
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};

export default Sidebar;
