/**
 * Data generation panel for configuring and running AI data generation
 */
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  X, 
  Play, 
  Download, 
  RefreshCw,
  CheckCircle,
  XCircle,
  Clock,
  Table
} from 'lucide-react';
import type { Workflow, DataTable } from '@/types';
import { GenerationStatus } from '@/types';
import { generationApi } from '@/services/api';
import { toast } from 'sonner';

interface GenerationPanelProps {
  workflow: Workflow;
  tables: DataTable[];
  onClose: () => void;
  onWorkflowUpdated: (workflow: Workflow) => void;
}

const GenerationPanel: React.FC<GenerationPanelProps> = ({
  workflow,
  tables,
  onClose,
  onWorkflowUpdated
}) => {
  const [recordCounts, setRecordCounts] = useState<Record<string, number>>(
    tables.reduce((acc, table) => ({ ...acc, [table.name]: 10 }), {})
  );
  const [generationStatus, setGenerationStatus] = useState<GenerationStatus | null>(null);
  const [taskId, setTaskId] = useState<number | null>(null);
  const [progress, setProgress] = useState(0);
  const [generatedData, setGeneratedData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const handleStartGeneration = async () => {
    if (tables.length === 0) {
      toast.error('请先添加数据表');
      return;
    }

    try {
      setLoading(true);
      setGenerationStatus(GenerationStatus.PENDING);
      setProgress(0);

      const response = await generationApi.generateData(workflow.id, {
        record_counts: recordCounts
      });

      setTaskId(response.task_id);
      setGenerationStatus(response.status as GenerationStatus);
      
      // Start polling for status updates
      pollGenerationStatus(response.task_id);
      
      toast.success('数据生成任务已启动');
    } catch (error) {
      console.error('Failed to start generation:', error);
      toast.error('启动数据生成失败');
      setGenerationStatus(null);
      setLoading(false);
    }
  };

  const pollGenerationStatus = async (taskId: number) => {
    const pollInterval = setInterval(async () => {
      try {
        const task = await generationApi.getGenerationTask(taskId);
        setGenerationStatus(task.status);
        
        // Update progress based on status
        switch (task.status) {
          case GenerationStatus.PENDING:
            setProgress(10);
            break;
          case GenerationStatus.RUNNING:
            setProgress(50);
            break;
          case GenerationStatus.COMPLETED:
            setProgress(100);
            setGeneratedData(task.generated_data);
            setLoading(false);
            clearInterval(pollInterval);
            toast.success('数据生成完成');
            break;
          case GenerationStatus.FAILED:
            setProgress(0);
            setLoading(false);
            clearInterval(pollInterval);
            toast.error(`数据生成失败: ${task.error_message || '未知错误'}`);
            break;
        }
      } catch (error) {
        console.error('Failed to poll generation status:', error);
        clearInterval(pollInterval);
        setLoading(false);
      }
    }, 2000);

    // Stop polling after 5 minutes
    setTimeout(() => {
      clearInterval(pollInterval);
      if (loading) {
        setLoading(false);
        toast.error('数据生成超时');
      }
    }, 300000);
  };

  const handleDownloadData = () => {
    if (!generatedData) return;

    const dataStr = JSON.stringify(generatedData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${workflow.name}_generated_data.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    toast.success('数据已下载');
  };

  const getStatusIcon = (status: GenerationStatus | null) => {
    switch (status) {
      case GenerationStatus.PENDING:
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case GenerationStatus.RUNNING:
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      case GenerationStatus.COMPLETED:
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case GenerationStatus.FAILED:
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Play className="w-4 h-4" />;
    }
  };

  const getStatusText = (status: GenerationStatus | null) => {
    switch (status) {
      case GenerationStatus.PENDING:
        return '等待中';
      case GenerationStatus.RUNNING:
        return '生成中';
      case GenerationStatus.COMPLETED:
        return '已完成';
      case GenerationStatus.FAILED:
        return '失败';
      default:
        return '准备就绪';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">数据生成</h3>
              <p className="text-sm text-gray-500">为 "{workflow.name}" 生成模拟数据</p>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <ScrollArea className="flex-1">
          <div className="p-6 space-y-6">
            {/* Generation status */}
            {generationStatus && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-2 mb-3">
                  {getStatusIcon(generationStatus)}
                  <span className="text-sm font-medium">
                    状态: {getStatusText(generationStatus)}
                  </span>
                </div>
                
                {loading && (
                  <div className="space-y-2">
                    <Progress value={progress} className="w-full" />
                    <p className="text-xs text-gray-500">
                      正在生成数据，请稍候...
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Table configuration */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900">生成配置</h4>
              
              {tables.length === 0 ? (
                <div className="text-center py-8">
                  <Table className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">
                    请先在工作流中添加数据表
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {tables.map((table) => (
                    <div key={table.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                      <Table className="w-4 h-4 text-blue-600" />
                      <div className="flex-1">
                        <span className="text-sm font-medium">{table.name}</span>
                        {table.alias && (
                          <span className="text-xs text-gray-500 ml-2">({table.alias})</span>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Label htmlFor={`count-${table.id}`} className="text-xs">
                          记录数:
                        </Label>
                        <Input
                          id={`count-${table.id}`}
                          type="number"
                          min="1"
                          max="1000"
                          value={recordCounts[table.name] || 10}
                          onChange={(e) => setRecordCounts(prev => ({
                            ...prev,
                            [table.name]: parseInt(e.target.value) || 10
                          }))}
                          className="w-20 h-8"
                          disabled={loading}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Generated data preview */}
            {generatedData && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-900">生成结果</h4>
                  <Button size="sm" onClick={handleDownloadData}>
                    <Download className="w-4 h-4 mr-2" />
                    下载数据
                  </Button>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4 max-h-60 overflow-auto">
                  <pre className="text-xs text-gray-700">
                    {JSON.stringify(generatedData, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200">
          <div className="flex items-center justify-end space-x-3">
            <Button variant="outline" onClick={onClose}>
              关闭
            </Button>
            <Button 
              onClick={handleStartGeneration}
              disabled={loading || tables.length === 0}
            >
              {loading ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  生成中...
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  开始生成
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GenerationPanel;
