/**
 * Custom table node component for ReactFlow
 */
import React from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  Settings, 
  Trash2, 
  Key,
  Hash,
  Type,
  Calendar,
  ToggleLeft
} from 'lucide-react';
import type { DataTable } from '@/types';
import { FieldType } from '@/types';

interface TableNodeData {
  table: DataTable;
  isSelected: boolean;
  onSelect: () => void;
  onDelete: () => void;
}

const getFieldTypeIcon = (type: FieldType) => {
  switch (type) {
    case FieldType.TEXT:
      return <Type className="w-3 h-3" />;
    case FieldType.NUMBER:
      return <Hash className="w-3 h-3" />;
    case FieldType.DATE:
    case FieldType.DATETIME:
      return <Calendar className="w-3 h-3" />;
    case FieldType.BOOLEAN:
      return <ToggleLeft className="w-3 h-3" />;
    default:
      return <Type className="w-3 h-3" />;
  }
};

const TableNode: React.FC<NodeProps<TableNodeData>> = ({ data, selected }) => {
  const { table, isSelected, onSelect, onDelete } = data;

  return (
    <div 
      className={`bg-white rounded-lg shadow-lg border-2 transition-all ${
        selected || isSelected 
          ? 'border-blue-500 shadow-blue-200' 
          : 'border-gray-200 hover:border-gray-300'
      }`}
      style={{ minWidth: 280, maxWidth: 320 }}
      onClick={onSelect}
    >
      {/* Connection handles */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-blue-500"
      />
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-blue-500"
      />
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 !bg-blue-500"
      />
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 !bg-blue-500"
      />

      {/* Header */}
      <div className="p-3 border-b border-gray-200 bg-gray-50 rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Table className="w-4 h-4 text-blue-600" />
            <div>
              <h3 className="text-sm font-semibold text-gray-900">
                {table.name}
              </h3>
              {table.alias && (
                <p className="text-xs text-gray-500">{table.alias}</p>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onSelect();
              }}
              className="h-6 w-6 p-0"
            >
              <Settings className="w-3 h-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
              className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
            >
              <Trash2 className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </div>

      {/* Fields preview */}
      <div className="p-3">
        <div className="space-y-2">
          {/* Sample fields - in real implementation, load from API */}
          <div className="flex items-center space-x-2 text-xs">
            <Key className="w-3 h-3 text-yellow-500" />
            <span className="font-medium">id</span>
            <Badge variant="secondary" className="text-xs px-1 py-0">
              number
            </Badge>
          </div>
          
          <div className="text-xs text-gray-500 text-center py-2">
            点击编辑字段...
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="px-3 py-2 bg-gray-50 rounded-b-lg border-t border-gray-200">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>0 字段</span>
          <span>表 #{table.id}</span>
        </div>
      </div>
    </div>
  );
};

export default TableNode;
