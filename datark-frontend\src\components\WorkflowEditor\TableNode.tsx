/**
 * Custom table node component for ReactFlow
 */
import React from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  Settings, 
  Trash2, 
  Key,
  Hash,
  Type,
  Calendar,
  ToggleLeft
} from 'lucide-react';
import type { DataTable } from '@/types';
import { FieldType } from '@/types';

interface TableNodeData {
  table: DataTable;
  isSelected: boolean;
  onSelect: () => void;
  onDelete: () => void;
}

const getFieldTypeIcon = (type: FieldType) => {
  switch (type) {
    case FieldType.TEXT:
      return <Type className="w-3 h-3" />;
    case FieldType.NUMBER:
      return <Hash className="w-3 h-3" />;
    case FieldType.DATE:
    case FieldType.DATETIME:
      return <Calendar className="w-3 h-3" />;
    case FieldType.BOOLEAN:
      return <ToggleLeft className="w-3 h-3" />;
    default:
      return <Type className="w-3 h-3" />;
  }
};

const TableNode: React.FC<NodeProps<TableNodeData>> = ({ data, selected }) => {
  const { table, isSelected, onSelect, onDelete } = data;

  return (
    <div
      className={`card-elevated transition-all duration-200 cursor-pointer group ${
        selected || isSelected
          ? 'border-primary shadow-glow scale-105'
          : 'border-border hover:border-primary/50 hover:shadow-medium hover:scale-102'
      }`}
      style={{ minWidth: 300, maxWidth: 340 }}
      onClick={onSelect}
    >
      {/* Enhanced Connection handles */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-primary border-2 !border-background transition-all duration-200 hover:!bg-primary/80 hover:scale-125"
      />
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-primary border-2 !border-background transition-all duration-200 hover:!bg-primary/80 hover:scale-125"
      />
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 !bg-primary border-2 !border-background transition-all duration-200 hover:!bg-primary/80 hover:scale-125"
      />
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 !bg-primary border-2 !border-background transition-all duration-200 hover:!bg-primary/80 hover:scale-125"
      />

      {/* Enhanced Header */}
      <div className="p-4 border-b border-border bg-muted/30 rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
              <Table className="w-4 h-4 text-primary" />
            </div>
            <div>
              <h3 className="text-sm font-semibold text-card-foreground">
                {table.name}
              </h3>
              {table.alias && (
                <p className="text-xs text-muted-foreground">{table.alias}</p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onSelect();
              }}
              className="h-7 w-7 p-0 hover:bg-primary/10 hover:text-primary transition-colors duration-200"
            >
              <Settings className="w-3.5 h-3.5" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
              className="h-7 w-7 p-0 text-muted-foreground hover:bg-error/10 hover:text-error transition-colors duration-200"
            >
              <Trash2 className="w-3.5 h-3.5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Fields preview */}
      <div className="p-4">
        <div className="space-y-3">
          {/* Sample fields - in real implementation, load from API */}
          <div className="flex items-center space-x-3 text-sm p-2 rounded-md bg-muted/20 hover:bg-muted/40 transition-colors duration-200">
            <div className="w-5 h-5 bg-warning/20 rounded flex items-center justify-center">
              <Key className="w-3 h-3 text-warning-600" />
            </div>
            <span className="font-medium text-card-foreground">id</span>
            <Badge variant="secondary" className="text-xs px-2 py-0.5 bg-primary/10 text-primary border-primary/20">
              number
            </Badge>
          </div>

          <div className="text-sm text-muted-foreground text-center py-4 border-2 border-dashed border-muted/50 rounded-lg hover:border-primary/30 hover:bg-primary/5 transition-all duration-200 cursor-pointer">
            <Plus className="w-4 h-4 mx-auto mb-1 opacity-50" />
            点击编辑字段...
          </div>
        </div>
      </div>

      {/* Enhanced Footer */}
      <div className="px-4 py-3 bg-muted/20 rounded-b-lg border-t border-border">
        <div className="flex items-center justify-between text-xs">
          <span className="text-muted-foreground flex items-center gap-1">
            <div className="status-dot status-info"></div>
            0 字段
          </span>
          <span className="text-muted-foreground font-mono">#{table.id}</span>
        </div>
      </div>
    </div>
  );
};

export default TableNode;
