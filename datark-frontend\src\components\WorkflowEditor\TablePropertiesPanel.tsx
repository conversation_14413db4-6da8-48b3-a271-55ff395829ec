/**
 * Table properties panel for editing table details and fields
 */
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { 
  X, 
  Plus, 
  Edit, 
  Trash2, 
  Save,
  Key,
  Hash,
  Type,
  Calendar,
  ToggleLeft,
  Mail,
  Phone,
  Link,
  List,
  Code
} from 'lucide-react';
import type { DataTable, TableField } from '@/types';
import { FieldType } from '@/types';
import { fieldApi } from '@/services/api';
import { toast } from 'sonner';

interface TablePropertiesPanelProps {
  table: DataTable;
  onClose: () => void;
  onTableUpdate: (tableId: number, updates: Partial<DataTable>) => void;
}

const getFieldTypeIcon = (type: FieldType) => {
  switch (type) {
    case FieldType.TEXT:
      return <Type className="w-4 h-4" />;
    case FieldType.NUMBER:
      return <Hash className="w-4 h-4" />;
    case FieldType.DATE:
    case FieldType.DATETIME:
      return <Calendar className="w-4 h-4" />;
    case FieldType.BOOLEAN:
      return <ToggleLeft className="w-4 h-4" />;
    case FieldType.EMAIL:
      return <Mail className="w-4 h-4" />;
    case FieldType.PHONE:
      return <Phone className="w-4 h-4" />;
    case FieldType.URL:
      return <Link className="w-4 h-4" />;
    case FieldType.ENUM:
      return <List className="w-4 h-4" />;
    case FieldType.JSON:
      return <Code className="w-4 h-4" />;
    default:
      return <Type className="w-4 h-4" />;
  }
};

const TablePropertiesPanel: React.FC<TablePropertiesPanelProps> = ({
  table,
  onClose,
  onTableUpdate
}) => {
  const [tableName, setTableName] = useState(table.name);
  const [tableAlias, setTableAlias] = useState(table.alias || '');
  const [fields, setFields] = useState<TableField[]>([]);
  const [loading, setLoading] = useState(false);

  // Load fields when table changes
  useEffect(() => {
    loadFields();
  }, [table.id]);

  const loadFields = async () => {
    try {
      setLoading(true);
      const fieldsData = await fieldApi.getFieldsByTable(table.id);
      setFields(fieldsData);
    } catch (error) {
      console.error('Failed to load fields:', error);
      toast.error('加载字段失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveTable = async () => {
    if (!tableName.trim()) {
      toast.error('表名不能为空');
      return;
    }

    try {
      await onTableUpdate(table.id, {
        name: tableName.trim(),
        alias: tableAlias.trim() || undefined
      });
      toast.success('表信息已保存');
    } catch (error) {
      console.error('Failed to save table:', error);
      toast.error('保存表信息失败');
    }
  };

  const handleAddField = async () => {
    try {
      const newField = await fieldApi.createField({
        table_id: table.id,
        name: `字段${fields.length + 1}`,
        field_type: FieldType.TEXT,
        order_index: fields.length
      });
      
      setFields(prev => [...prev, newField]);
      toast.success('字段创建成功');
    } catch (error) {
      console.error('Failed to create field:', error);
      toast.error('创建字段失败');
    }
  };

  const handleDeleteField = async (fieldId: number) => {
    try {
      await fieldApi.deleteField(fieldId);
      setFields(prev => prev.filter(f => f.id !== fieldId));
      toast.success('字段删除成功');
    } catch (error) {
      console.error('Failed to delete field:', error);
      toast.error('删除字段失败');
    }
  };

  return (
    <div className="w-96 bg-white border-l border-gray-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">表属性</h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-6">
          {/* Table basic info */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-900">基本信息</h4>
            
            <div className="space-y-2">
              <Label htmlFor="tableName">表名</Label>
              <Input
                id="tableName"
                value={tableName}
                onChange={(e) => setTableName(e.target.value)}
                placeholder="输入表名"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="tableAlias">别名</Label>
              <Input
                id="tableAlias"
                value={tableAlias}
                onChange={(e) => setTableAlias(e.target.value)}
                placeholder="输入表别名（可选）"
              />
            </div>
            
            <Button onClick={handleSaveTable} size="sm" className="w-full">
              <Save className="w-4 h-4 mr-2" />
              保存表信息
            </Button>
          </div>

          {/* Fields section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-900">字段列表</h4>
              <Button size="sm" onClick={handleAddField}>
                <Plus className="w-4 h-4 mr-2" />
                添加字段
              </Button>
            </div>

            {loading ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              </div>
            ) : fields.length === 0 ? (
              <div className="text-center py-8">
                <Type className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500 mb-4">还没有字段</p>
                <Button size="sm" onClick={handleAddField}>
                  添加第一个字段
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                {fields.map((field) => (
                  <div
                    key={field.id}
                    className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {getFieldTypeIcon(field.field_type)}
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium">{field.name}</span>
                            {field.is_primary_key && (
                              <Key className="w-3 h-3 text-yellow-500" />
                            )}
                          </div>
                          <div className="flex items-center space-x-1 mt-1">
                            <Badge variant="secondary" className="text-xs">
                              {field.field_type}
                            </Badge>
                            {field.is_required && (
                              <Badge variant="outline" className="text-xs">
                                必填
                              </Badge>
                            )}
                            {field.is_unique && (
                              <Badge variant="outline" className="text-xs">
                                唯一
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <Button variant="ghost" size="sm">
                          <Edit className="w-3 h-3" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleDeleteField(field.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                    
                    {field.ai_prompt && (
                      <p className="text-xs text-gray-500 mt-2">
                        AI提示: {field.ai_prompt}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </ScrollArea>
    </div>
  );
};

export default TablePropertiesPanel;
