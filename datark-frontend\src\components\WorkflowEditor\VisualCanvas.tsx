/**
 * Visual canvas component using React<PERSON><PERSON> for drag-and-drop table editing
 */
import React, { useCallback, useMemo } from 'react';
import {
  ReactFlow,
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
  MiniMap,
  Panel
} from 'reactflow';
import 'reactflow/dist/style.css';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import type { DataTable } from '@/types';
import TableNode from './TableNode';

interface VisualCanvasProps {
  tables: DataTable[];
  onTableCreate: (position: { x: number; y: number }) => void;
  onTableSelect: (table: DataTable) => void;
  onTableUpdate: (tableId: number, updates: Partial<DataTable>) => void;
  onTableDelete: (tableId: number) => void;
  selectedTableId?: number;
}

// Custom node types
const nodeTypes = {
  tableNode: TableNode,
};

const VisualCanvas: React.FC<VisualCanvasProps> = ({
  tables,
  onTableCreate,
  onTableSelect,
  onTableUpdate,
  onTableDelete,
  selectedTableId
}) => {
  // Convert tables to ReactFlow nodes
  const initialNodes: Node[] = useMemo(() => {
    return tables.map((table) => ({
      id: table.id.toString(),
      type: 'tableNode',
      position: { x: table.position_x, y: table.position_y },
      data: {
        table,
        isSelected: table.id === selectedTableId,
        onSelect: () => onTableSelect(table),
        onDelete: () => onTableDelete(table.id),
      },
      selected: table.id === selectedTableId,
    }));
  }, [tables, selectedTableId, onTableSelect, onTableDelete]);

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  // Update nodes when tables change
  React.useEffect(() => {
    const newNodes = tables.map((table) => ({
      id: table.id.toString(),
      type: 'tableNode',
      position: { x: table.position_x, y: table.position_y },
      data: {
        table,
        isSelected: table.id === selectedTableId,
        onSelect: () => onTableSelect(table),
        onDelete: () => onTableDelete(table.id),
      },
      selected: table.id === selectedTableId,
    }));
    setNodes(newNodes);
  }, [tables, selectedTableId, onTableSelect, onTableDelete, setNodes]);

  // Handle node drag end to update table position
  const handleNodeDragStop = useCallback(
    (event: React.MouseEvent, node: Node) => {
      const tableId = parseInt(node.id);
      onTableUpdate(tableId, {
        position_x: Math.round(node.position.x),
        position_y: Math.round(node.position.y),
      });
    },
    [onTableUpdate]
  );

  // Handle edge connections (for future relationship support)
  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // Handle canvas click to create new table
  const handleCanvasClick = useCallback(
    (event: React.MouseEvent) => {
      const target = event.target as HTMLElement;
      
      // Only create table if clicking on the background
      if (target.classList.contains('react-flow__pane')) {
        const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
        const position = {
          x: event.clientX - rect.left - 150, // Center the node
          y: event.clientY - rect.top - 100,
        };
        onTableCreate(position);
      }
    },
    [onTableCreate]
  );

  return (
    <div className="w-full h-full relative">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeDragStop={handleNodeDragStop}
        nodeTypes={nodeTypes}
        onClick={handleCanvasClick}
        fitView
        attributionPosition="bottom-left"
      >
        <Background variant={BackgroundVariant.Dots} gap={20} size={1} />
        <Controls />
        <MiniMap 
          nodeColor="#3b82f6"
          maskColor="rgba(0, 0, 0, 0.1)"
          position="top-right"
        />
        
        {/* Add table button */}
        <Panel position="top-left">
          <div className="bg-white rounded-lg shadow-lg p-4 border border-gray-200">
            <h3 className="text-sm font-medium text-gray-900 mb-3">工具箱</h3>
            <Button
              size="sm"
              onClick={() => onTableCreate({ x: 100, y: 100 })}
              className="w-full flex items-center justify-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>添加数据表</span>
            </Button>
          </div>
        </Panel>

        {/* Instructions */}
        {tables.length === 0 && (
          <Panel position="center">
            <div className="bg-white rounded-lg shadow-lg p-6 border border-gray-200 text-center max-w-md">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Plus className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                开始设计数据模型
              </h3>
              <p className="text-gray-500 mb-4">
                点击画布任意位置或使用左上角的工具箱来添加数据表
              </p>
              <Button onClick={() => onTableCreate({ x: 200, y: 200 })}>
                添加第一个数据表
              </Button>
            </div>
          </Panel>
        )}
      </ReactFlow>
    </div>
  );
};

export default VisualCanvas;
