/**
 * Visual canvas component using React<PERSON><PERSON> for drag-and-drop table editing
 */
import React, { useCallback, useMemo } from 'react';
import {
  ReactFlow,
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
  MiniMap,
  Panel
} from 'reactflow';
import 'reactflow/dist/style.css';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import type { DataTable } from '@/types';
import TableNode from './TableNode';

interface VisualCanvasProps {
  tables: DataTable[];
  onTableCreate: (position: { x: number; y: number }) => void;
  onTableSelect: (table: DataTable) => void;
  onTableUpdate: (tableId: number, updates: Partial<DataTable>) => void;
  onTableDelete: (tableId: number) => void;
  selectedTableId?: number;
}

// Custom node types
const nodeTypes = {
  tableNode: TableNode,
};

const VisualCanvas: React.FC<VisualCanvasProps> = ({
  tables,
  onTableCreate,
  onTableSelect,
  onTableUpdate,
  onTableDelete,
  selectedTableId
}) => {
  // Convert tables to ReactFlow nodes
  const initialNodes: Node[] = useMemo(() => {
    return tables.map((table) => ({
      id: table.id.toString(),
      type: 'tableNode',
      position: { x: table.position_x, y: table.position_y },
      data: {
        table,
        isSelected: table.id === selectedTableId,
        onSelect: () => onTableSelect(table),
        onDelete: () => onTableDelete(table.id),
      },
      selected: table.id === selectedTableId,
    }));
  }, [tables, selectedTableId, onTableSelect, onTableDelete]);

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  // Update nodes when tables change
  React.useEffect(() => {
    const newNodes = tables.map((table) => ({
      id: table.id.toString(),
      type: 'tableNode',
      position: { x: table.position_x, y: table.position_y },
      data: {
        table,
        isSelected: table.id === selectedTableId,
        onSelect: () => onTableSelect(table),
        onDelete: () => onTableDelete(table.id),
      },
      selected: table.id === selectedTableId,
    }));
    setNodes(newNodes);
  }, [tables, selectedTableId, onTableSelect, onTableDelete, setNodes]);

  // Handle node drag end to update table position
  const handleNodeDragStop = useCallback(
    (event: React.MouseEvent, node: Node) => {
      const tableId = parseInt(node.id);
      onTableUpdate(tableId, {
        position_x: Math.round(node.position.x),
        position_y: Math.round(node.position.y),
      });
    },
    [onTableUpdate]
  );

  // Handle edge connections (for future relationship support)
  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // Handle canvas click to create new table
  const handleCanvasClick = useCallback(
    (event: React.MouseEvent) => {
      const target = event.target as HTMLElement;
      
      // Only create table if clicking on the background
      if (target.classList.contains('react-flow__pane')) {
        const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
        const position = {
          x: event.clientX - rect.left - 150, // Center the node
          y: event.clientY - rect.top - 100,
        };
        onTableCreate(position);
      }
    },
    [onTableCreate]
  );

  return (
    <div className="w-full h-full relative bg-background">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeDragStop={handleNodeDragStop}
        nodeTypes={nodeTypes}
        onClick={handleCanvasClick}
        fitView
        attributionPosition="bottom-left"
        className="bg-background"
        style={{
          background: 'hsl(var(--background))',
        }}
      >
        <Background
          variant={BackgroundVariant.Dots}
          gap={24}
          size={1.5}
          color="hsl(var(--muted-foreground))"
          style={{
            opacity: 0.3,
          }}
        />
        <Controls
          className="bg-card border border-border shadow-medium rounded-lg"
          style={{
            button: {
              backgroundColor: 'hsl(var(--card))',
              borderColor: 'hsl(var(--border))',
              color: 'hsl(var(--card-foreground))',
            }
          }}
        />
        <MiniMap
          nodeColor="hsl(var(--primary))"
          maskColor="hsl(var(--muted) / 0.1)"
          position="top-right"
          className="bg-card border border-border shadow-medium rounded-lg overflow-hidden"
          style={{
            backgroundColor: 'hsl(var(--card))',
          }}
        />

        {/* Enhanced Add table button */}
        <Panel position="top-left">
          <div className="card-elevated p-4 animate-in">
            <h3 className="text-sm font-semibold text-card-foreground mb-3 flex items-center gap-2">
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              工具箱
            </h3>
            <Button
              size="sm"
              onClick={() => onTableCreate({ x: 100, y: 100 })}
              className="w-full flex items-center justify-center gap-2 btn-primary transition-all duration-200 hover:scale-105"
            >
              <Plus className="w-4 h-4" />
              <span>添加数据表</span>
            </Button>
          </div>
        </Panel>

        {/* Enhanced Instructions */}
        {tables.length === 0 && (
          <Panel position="center">
            <div className="card-elevated p-8 text-center max-w-md animate-in">
              <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse">
                <Plus className="w-10 h-10 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-card-foreground mb-3">
                开始设计数据模型
              </h3>
              <p className="text-muted-foreground mb-6 leading-relaxed">
                点击画布任意位置或使用左上角的工具箱来添加数据表，开始构建您的数据模型
              </p>
              <Button
                onClick={() => onTableCreate({ x: 200, y: 200 })}
                className="btn-primary transition-all duration-200 hover:scale-105"
                size="lg"
              >
                <Plus className="w-5 h-5 mr-2" />
                添加第一个数据表
              </Button>
            </div>
          </Panel>
        )}

        {/* Canvas info panel */}
        {tables.length > 0 && (
          <Panel position="bottom-left">
            <div className="bg-card/80 backdrop-blur-sm border border-border rounded-lg px-3 py-2 text-xs text-muted-foreground">
              共 {tables.length} 个数据表
            </div>
          </Panel>
        )}
      </ReactFlow>
    </div>
  );
};

export default VisualCanvas;
