/**
 * Main workflow editor component with visual canvas
 */
import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Save, 
  Download, 
  Settings, 
  Plus,
  Trash2,
  Edit
} from 'lucide-react';
import type { Workflow, DataTable } from '@/types';
import { WorkflowStatus } from '@/types';
import { workflowApi, tableApi } from '@/services/api';
// import VisualCanvas from './VisualCanvas';
import TablePropertiesPanel from './TablePropertiesPanel';
import GenerationPanel from './GenerationPanel';
import { toast } from 'sonner';

interface WorkflowEditorProps {
  workflow: Workflow;
  onWorkflowUpdated: (workflow: Workflow) => void;
  onWorkflowDeleted: (workflowId: number) => void;
}

const WorkflowEditor: React.FC<WorkflowEditorProps> = ({
  workflow,
  onWorkflowUpdated,
  onWorkflowDeleted
}) => {
  const [tables, setTables] = useState<DataTable[]>([]);
  const [selectedTable, setSelectedTable] = useState<DataTable | null>(null);
  const [showPropertiesPanel, setShowPropertiesPanel] = useState(false);
  const [showGenerationPanel, setShowGenerationPanel] = useState(false);
  const [loading, setLoading] = useState(false);

  // Load tables when workflow changes
  useEffect(() => {
    if (workflow) {
      loadTables();
    }
  }, [workflow]);

  const loadTables = async () => {
    try {
      setLoading(true);
      const tablesData = await tableApi.getTablesByWorkflow(workflow.id);
      setTables(tablesData);
    } catch (error) {
      console.error('Failed to load tables:', error);
      toast.error('加载数据表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTable = useCallback(async (position: { x: number; y: number }) => {
    try {
      const newTable = await tableApi.createTable({
        workflow_id: workflow.id,
        name: `表${tables.length + 1}`,
        position_x: position.x,
        position_y: position.y
      });
      
      setTables(prev => [...prev, newTable]);
      setSelectedTable(newTable);
      setShowPropertiesPanel(true);
      toast.success('数据表创建成功');
    } catch (error) {
      console.error('Failed to create table:', error);
      toast.error('创建数据表失败');
    }
  }, [workflow.id, tables.length]);

  const handleTableSelect = useCallback((table: DataTable) => {
    setSelectedTable(table);
    setShowPropertiesPanel(true);
  }, []);

  const handleTableUpdate = useCallback(async (tableId: number, updates: Partial<DataTable>) => {
    try {
      const updatedTable = await tableApi.updateTable(tableId, updates);
      setTables(prev => prev.map(t => t.id === tableId ? updatedTable : t));
      
      if (selectedTable?.id === tableId) {
        setSelectedTable(updatedTable);
      }
    } catch (error) {
      console.error('Failed to update table:', error);
      toast.error('更新数据表失败');
    }
  }, [selectedTable]);

  const handleTableDelete = useCallback(async (tableId: number) => {
    try {
      await tableApi.deleteTable(tableId);
      setTables(prev => prev.filter(t => t.id !== tableId));
      
      if (selectedTable?.id === tableId) {
        setSelectedTable(null);
        setShowPropertiesPanel(false);
      }
      
      toast.success('数据表删除成功');
    } catch (error) {
      console.error('Failed to delete table:', error);
      toast.error('删除数据表失败');
    }
  }, [selectedTable]);

  const handleStartGeneration = () => {
    setShowGenerationPanel(true);
  };

  const handleSaveWorkflow = async () => {
    try {
      // Auto-save is handled by individual table updates
      toast.success('工作流已保存');
    } catch (error) {
      console.error('Failed to save workflow:', error);
      toast.error('保存工作流失败');
    }
  };

  const getStatusColor = (status: WorkflowStatus) => {
    switch (status) {
      case WorkflowStatus.DRAFT:
        return 'bg-gray-100 text-gray-800';
      case WorkflowStatus.RUNNING:
        return 'bg-blue-100 text-blue-800';
      case WorkflowStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case WorkflowStatus.FAILED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex-1 flex flex-col">
      {/* Toolbar */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">{workflow.name}</h2>
              {workflow.description && (
                <p className="text-sm text-gray-500">{workflow.description}</p>
              )}
            </div>
            <Badge className={getStatusColor(workflow.status)}>
              {workflow.status}
            </Badge>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleSaveWorkflow}>
              <Save className="w-4 h-4 mr-2" />
              保存
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleStartGeneration}
              disabled={tables.length === 0}
            >
              <Play className="w-4 h-4 mr-2" />
              生成数据
            </Button>
            
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              导出
            </Button>
            
            <Button variant="outline" size="sm">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main editor area */}
      <div className="flex-1 flex">
        {/* Canvas */}
        <div className="flex-1 relative">
          <div className="h-full flex items-center justify-center bg-gray-50">
            <p className="text-gray-500">Visual Canvas (temporarily disabled)</p>
          </div>
        </div>

        {/* Properties panel */}
        {showPropertiesPanel && selectedTable && (
          <TablePropertiesPanel
            table={selectedTable}
            onClose={() => setShowPropertiesPanel(false)}
            onTableUpdate={handleTableUpdate}
          />
        )}
      </div>

      {/* Generation panel */}
      {showGenerationPanel && (
        <GenerationPanel
          workflow={workflow}
          tables={tables}
          onClose={() => setShowGenerationPanel(false)}
          onWorkflowUpdated={onWorkflowUpdated}
        />
      )}
    </div>
  );
};

export default WorkflowEditor;
