/**
 * Main workflow editor component with visual canvas
 */
import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Save, 
  Download, 
  Settings, 
  Plus,
  Trash2,
  Edit
} from 'lucide-react';
import type { Workflow, DataTable } from '@/types';
import { WorkflowStatus } from '@/types';
import { workflowApi, tableApi } from '@/services/api';
import VisualCanvas from './VisualCanvas';
import TablePropertiesPanel from './TablePropertiesPanel';
import GenerationPanel from './GenerationPanel';
import { toast } from 'sonner';

interface WorkflowEditorProps {
  workflow: Workflow;
  onWorkflowUpdated: (workflow: Workflow) => void;
  onWorkflowDeleted: (workflowId: number) => void;
}

const WorkflowEditor: React.FC<WorkflowEditorProps> = ({
  workflow,
  onWorkflowUpdated,
  onWorkflowDeleted
}) => {
  const [tables, setTables] = useState<DataTable[]>([]);
  const [selectedTable, setSelectedTable] = useState<DataTable | null>(null);
  const [showPropertiesPanel, setShowPropertiesPanel] = useState(false);
  const [showGenerationPanel, setShowGenerationPanel] = useState(false);
  const [loading, setLoading] = useState(false);

  // Load tables when workflow changes
  useEffect(() => {
    if (workflow) {
      loadTables();
    }
  }, [workflow]);

  const loadTables = async () => {
    try {
      setLoading(true);
      const tablesData = await tableApi.getTablesByWorkflow(workflow.id);
      setTables(tablesData);
    } catch (error) {
      console.error('Failed to load tables:', error);
      toast.error('加载数据表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTable = useCallback(async (position: { x: number; y: number }) => {
    try {
      const newTable = await tableApi.createTable({
        workflow_id: workflow.id,
        name: `表${tables.length + 1}`,
        position_x: position.x,
        position_y: position.y
      });
      
      setTables(prev => [...prev, newTable]);
      setSelectedTable(newTable);
      setShowPropertiesPanel(true);
      toast.success('数据表创建成功');
    } catch (error) {
      console.error('Failed to create table:', error);
      toast.error('创建数据表失败');
    }
  }, [workflow.id, tables.length]);

  const handleTableSelect = useCallback((table: DataTable) => {
    setSelectedTable(table);
    setShowPropertiesPanel(true);
  }, []);

  const handleTableUpdate = useCallback(async (tableId: number, updates: Partial<DataTable>) => {
    try {
      const updatedTable = await tableApi.updateTable(tableId, updates);
      setTables(prev => prev.map(t => t.id === tableId ? updatedTable : t));
      
      if (selectedTable?.id === tableId) {
        setSelectedTable(updatedTable);
      }
    } catch (error) {
      console.error('Failed to update table:', error);
      toast.error('更新数据表失败');
    }
  }, [selectedTable]);

  const handleTableDelete = useCallback(async (tableId: number) => {
    try {
      await tableApi.deleteTable(tableId);
      setTables(prev => prev.filter(t => t.id !== tableId));
      
      if (selectedTable?.id === tableId) {
        setSelectedTable(null);
        setShowPropertiesPanel(false);
      }
      
      toast.success('数据表删除成功');
    } catch (error) {
      console.error('Failed to delete table:', error);
      toast.error('删除数据表失败');
    }
  }, [selectedTable]);

  const handleStartGeneration = () => {
    setShowGenerationPanel(true);
  };

  const handleSaveWorkflow = async () => {
    try {
      // Auto-save is handled by individual table updates
      toast.success('工作流已保存');
    } catch (error) {
      console.error('Failed to save workflow:', error);
      toast.error('保存工作流失败');
    }
  };

  const getStatusColor = (status: WorkflowStatus) => {
    switch (status) {
      case WorkflowStatus.DRAFT:
        return 'bg-gray-100 text-gray-800';
      case WorkflowStatus.RUNNING:
        return 'bg-blue-100 text-blue-800';
      case WorkflowStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case WorkflowStatus.FAILED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex-1 flex flex-col">
      {/* Enhanced Toolbar - Responsive */}
      <div className="bg-card border-b border-border px-4 md:px-6 py-4 md:py-5 shadow-soft">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 md:space-x-6 flex-1 min-w-0">
            <div className="min-w-0 flex-1">
              <h2 className="text-lg md:text-xl font-bold text-card-foreground truncate">{workflow.name}</h2>
              {workflow.description && (
                <p className="hidden md:block text-sm text-muted-foreground mt-1 truncate">{workflow.description}</p>
              )}
            </div>
            <Badge className={`${getStatusColor(workflow.status)} px-2 md:px-3 py-1 text-xs font-medium flex-shrink-0`}>
              {workflow.status}
            </Badge>
          </div>

          <div className="flex items-center space-x-2 md:space-x-3 ml-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSaveWorkflow}
              className="hover:bg-muted/50 transition-all duration-200 hover:scale-105 hidden sm:flex"
            >
              <Save className="w-4 h-4 mr-2" />
              保存
            </Button>

            <Button
              size="sm"
              onClick={handleStartGeneration}
              disabled={tables.length === 0}
              className="btn-primary shadow-soft hover:shadow-medium transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Play className="w-4 h-4 md:mr-2" />
              <span className="hidden md:inline">生成数据</span>
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="hover:bg-muted/50 transition-all duration-200 hover:scale-105 hidden lg:flex"
            >
              <Download className="w-4 h-4 mr-2" />
              导出
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="hover:bg-muted/50 transition-all duration-200 hover:scale-105 w-9 h-9 p-0"
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main editor area */}
      <div className="flex-1 flex">
        {/* Canvas */}
        <div className="flex-1 relative bg-muted/30">
          {loading ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <div className="loading-spinner h-8 w-8 mx-auto mb-4"></div>
                <p className="text-muted-foreground">加载中...</p>
              </div>
            </div>
          ) : (
            <VisualCanvas
              tables={tables}
              onTableCreate={handleCreateTable}
              onTableSelect={handleTableSelect}
              onTableUpdate={handleTableUpdate}
              onTableDelete={handleTableDelete}
              selectedTableId={selectedTable?.id}
            />
          )}
        </div>

        {/* Properties panel */}
        {showPropertiesPanel && selectedTable && (
          <TablePropertiesPanel
            table={selectedTable}
            onClose={() => setShowPropertiesPanel(false)}
            onTableUpdate={handleTableUpdate}
          />
        )}
      </div>

      {/* Generation panel */}
      {showGenerationPanel && (
        <GenerationPanel
          workflow={workflow}
          tables={tables}
          onClose={() => setShowGenerationPanel(false)}
          onWorkflowUpdated={onWorkflowUpdated}
        />
      )}
    </div>
  );
};

export default WorkflowEditor;
