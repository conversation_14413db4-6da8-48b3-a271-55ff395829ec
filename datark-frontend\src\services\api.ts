/**
 * API service for DatArk backend communication
 */
import axios from 'axios';
import type {
  Workflow,
  WorkflowCreate,
  WorkflowUpdate,
  WorkflowListResponse,
  DataTable,
  DataTableCreate,
  TableField,
  TableFieldCreate,
  GenerationRequest,
  GenerationTask,
  ApiResponse
} from '@/types';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:8001/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth tokens if needed
api.interceptors.request.use(
  (config) => {
    // Add auth token here if needed
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// Workflow API
export const workflowApi = {
  // Get all workflows
  getWorkflows: async (skip = 0, limit = 100): Promise<WorkflowListResponse> => {
    const response = await api.get(`/workflows?skip=${skip}&limit=${limit}`);
    return response.data;
  },

  // Get workflow by ID
  getWorkflow: async (id: number): Promise<Workflow> => {
    const response = await api.get(`/workflows/${id}`);
    return response.data;
  },

  // Create new workflow
  createWorkflow: async (data: WorkflowCreate): Promise<Workflow> => {
    const response = await api.post('/workflows', data);
    return response.data;
  },

  // Update workflow
  updateWorkflow: async (id: number, data: WorkflowUpdate): Promise<Workflow> => {
    const response = await api.put(`/workflows/${id}`, data);
    return response.data;
  },

  // Delete workflow
  deleteWorkflow: async (id: number): Promise<void> => {
    await api.delete(`/workflows/${id}`);
  },
};

// Table API
export const tableApi = {
  // Get tables by workflow
  getTablesByWorkflow: async (workflowId: number): Promise<DataTable[]> => {
    const response = await api.get(`/tables/workflow/${workflowId}`);
    return response.data;
  },

  // Get table by ID
  getTable: async (id: number): Promise<DataTable> => {
    const response = await api.get(`/tables/${id}`);
    return response.data;
  },

  // Create new table
  createTable: async (data: DataTableCreate): Promise<DataTable> => {
    const response = await api.post('/tables', data);
    return response.data;
  },

  // Update table
  updateTable: async (id: number, data: Partial<DataTable>): Promise<DataTable> => {
    const response = await api.put(`/tables/${id}`, data);
    return response.data;
  },

  // Delete table
  deleteTable: async (id: number): Promise<void> => {
    await api.delete(`/tables/${id}`);
  },
};

// Field API
export const fieldApi = {
  // Get fields by table
  getFieldsByTable: async (tableId: number): Promise<TableField[]> => {
    const response = await api.get(`/fields/table/${tableId}`);
    return response.data;
  },

  // Get field by ID
  getField: async (id: number): Promise<TableField> => {
    const response = await api.get(`/fields/${id}`);
    return response.data;
  },

  // Create new field
  createField: async (data: TableFieldCreate): Promise<TableField> => {
    const response = await api.post('/fields', data);
    return response.data;
  },

  // Update field
  updateField: async (id: number, data: Partial<TableField>): Promise<TableField> => {
    const response = await api.put(`/fields/${id}`, data);
    return response.data;
  },

  // Delete field
  deleteField: async (id: number): Promise<void> => {
    await api.delete(`/fields/${id}`);
  },
};

// Generation API
export const generationApi = {
  // Start data generation
  generateData: async (workflowId: number, request: GenerationRequest): Promise<{ task_id: number; status: string }> => {
    const response = await api.post(`/generation/workflows/${workflowId}/generate`, request);
    return response.data;
  },

  // Get generation task status
  getGenerationTask: async (taskId: number): Promise<GenerationTask> => {
    const response = await api.get(`/generation/tasks/${taskId}`);
    return response.data;
  },
};

// Health check
export const healthApi = {
  check: async (): Promise<{ status: string; message: string }> => {
    const response = await api.get('/health');
    return response.data;
  },
};

export default api;
