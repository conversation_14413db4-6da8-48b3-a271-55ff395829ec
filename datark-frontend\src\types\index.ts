/**
 * Type definitions for DatArk application
 */

// Workflow related types
export enum WorkflowStatus {
  DRAFT = "draft",
  RUNNING = "running",
  COMPLETED = "completed",
  FAILED = "failed"
}

export interface Workflow {
  id: number;
  name: string;
  description?: string;
  status: WorkflowStatus;
  created_at: string;
  updated_at?: string;
}

export interface WorkflowCreate {
  name: string;
  description?: string;
}

export interface WorkflowUpdate {
  name?: string;
  description?: string;
  status?: WorkflowStatus;
}

// Data table related types
export interface DataTable {
  id: number;
  workflow_id: number;
  name: string;
  alias?: string;
  position_x: number;
  position_y: number;
  created_at: string;
  updated_at?: string;
}

export interface DataTableCreate {
  workflow_id: number;
  name: string;
  alias?: string;
  position_x?: number;
  position_y?: number;
}

// Table field related types
export interface TableField {
  id: number;
  table_id: number;
  name: string;
  field_type: FieldType;
  ai_prompt?: string;
  is_required: boolean;
  is_unique: boolean;
  is_primary_key: boolean;
  enum_options?: string;
  default_value?: string;
  order_index: number;
  created_at: string;
  updated_at?: string;
}

export enum FieldType {
  TEXT = "text",
  NUMBER = "number", 
  DATE = "date",
  DATETIME = "datetime",
  BOOLEAN = "boolean",
  EMAIL = "email",
  PHONE = "phone",
  URL = "url",
  ENUM = "enum",
  JSON = "json"
}

export interface TableFieldCreate {
  table_id: number;
  name: string;
  field_type: FieldType;
  ai_prompt?: string;
  is_required?: boolean;
  is_unique?: boolean;
  is_primary_key?: boolean;
  enum_options?: string;
  default_value?: string;
  order_index?: number;
}

// Table relationship types
export interface TableRelationship {
  id: number;
  workflow_id: number;
  relationship_type: RelationshipType;
  description?: string;
  source_table_id: number;
  target_table_id: number;
  created_at: string;
  updated_at?: string;
}

export enum RelationshipType {
  ONE_TO_ONE = "1:1",
  ONE_TO_MANY = "1:n", 
  MANY_TO_MANY = "n:m"
}

// Generation related types
export interface GenerationRequest {
  record_counts: Record<string, number>;
}

export interface GenerationTask {
  id: number;
  workflow_id: number;
  status: GenerationStatus;
  record_counts?: Record<string, number>;
  generated_data?: Record<string, any>;
  error_message?: string;
  started_at?: string;
  completed_at?: string;
  created_at: string;
}

export enum GenerationStatus {
  PENDING = "pending",
  RUNNING = "running",
  COMPLETED = "completed", 
  FAILED = "failed"
}

// API response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
}

export interface WorkflowListResponse {
  workflows: Workflow[];
  total: number;
}

// UI related types
export interface Position {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}
